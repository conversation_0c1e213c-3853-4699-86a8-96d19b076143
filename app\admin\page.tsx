import type { Metada<PERSON> } from "next"
import { AdminDashboard } from "@/components/admin/admin-dashboard"
import { redirect } from "next/navigation"
import { AdminPageClient } from "@/components/admin/admin-page-client"

export const metadata: Metadata = {
  title: "Admin Dashboard - PUBG Store",
  description: "Admin panel for managing accounts, hacks, and users.",
  robots: "noindex, nofollow", // Private admin page
}

// ## TODO: Check admin authentication with Supabase
const checkAdminAuth = async () => {
  // Placeholder - replace with actual auth check
  const isAdmin = true // This should check actual admin status
  return isAdmin
}

export default async function AdminPage() {
  const isAdmin = await checkAdminAuth()

  if (!isAdmin) {
    redirect("/auth")
  }

  return <AdminPageClient />
}
