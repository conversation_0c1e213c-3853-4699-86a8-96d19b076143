"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Download, 
  RefreshCw, 
  TrendingUp, 
  DollarSign, 
  Package, 
  Users,
  Calendar,
  CreditCard
} from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"
import { 
  Order, 
  OrderStatus, 
  PaymentStatus, 
  OrderStats,
  getOrderStatusColor, 
  getPaymentStatusColor 
} from "@/types/order"

export function OrdersManagement() {
  const { language, t } = useLanguage()
  
  // ## Database: Replace with actual API calls
  const [orders, setOrders] = useState<Order[]>([
    {
      id: "1",
      orderNumber: "ORD-240115-ABC123",
      userId: "user1",
      userEmail: "<EMAIL>",
      userName: "John Doe",
      status: "completed",
      paymentStatus: "paid",
      paymentMethod: "credit_card",
      subtotal: 49.99,
      taxAmount: 0,
      discountAmount: 0,
      totalAmount: 49.99,
      currency: "USD",
      paymentTransactionId: "txn_123456789",
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-15T11:00:00Z",
      completedAt: "2024-01-15T11:00:00Z",
      items: [
        {
          id: "item1",
          orderId: "1",
          productType: "hack",
          productId: "hack1",
          productName: {
            en: "PUBG Mobile Aimbot Pro",
            ar: "ايمبوت PUBG موبايل برو"
          },
          productDescription: {
            en: "Advanced aimbot with customizable settings",
            ar: "ايمبوت متقدم مع إعدادات قابلة للتخصيص"
          },
          unitPrice: 49.99,
          quantity: 1,
          totalPrice: 49.99,
          productImageUrl: "/placeholder.svg",
          createdAt: "2024-01-15T10:30:00Z"
        }
      ]
    },
    {
      id: "2",
      orderNumber: "ORD-240110-DEF456",
      userId: "user2",
      userEmail: "<EMAIL>",
      userName: "Jane Smith",
      status: "pending",
      paymentStatus: "pending",
      paymentMethod: "paypal",
      subtotal: 299.99,
      taxAmount: 0,
      discountAmount: 0,
      totalAmount: 299.99,
      currency: "USD",
      createdAt: "2024-01-10T14:20:00Z",
      updatedAt: "2024-01-10T14:20:00Z",
      items: [
        {
          id: "item2",
          orderId: "2",
          productType: "account",
          productId: "account1",
          productName: {
            en: "Premium PUBG Mobile Account",
            ar: "حساب PUBG موبايل مميز"
          },
          productDescription: {
            en: "High-level premium account with rare skins",
            ar: "حساب مميز عالي المستوى مع اسكنات نادرة"
          },
          unitPrice: 299.99,
          quantity: 1,
          totalPrice: 299.99,
          productImageUrl: "/placeholder.svg",
          createdAt: "2024-01-10T14:20:00Z"
        }
      ]
    }
  ])

  const [stats] = useState<OrderStats>({
    total: 156,
    pending: 12,
    processing: 8,
    completed: 128,
    cancelled: 6,
    refunded: 2,
    totalRevenue: 15420.50,
    averageOrderValue: 98.85,
    totalOrders: 156,
    recentOrders: [],
    topProducts: []
  })

  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<OrderStatus | "all">("all")
  const [paymentFilter, setPaymentFilter] = useState<PaymentStatus | "all">("all")
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)

  // Filter orders
  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.userName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || order.status === statusFilter
    const matchesPayment = paymentFilter === "all" || order.paymentStatus === paymentFilter
    return matchesSearch && matchesStatus && matchesPayment
  })

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order)
    setIsViewModalOpen(true)
  }

  const handleUpdateOrderStatus = async (orderId: string, newStatus: OrderStatus) => {
    // ## Database: Update order status in Supabase
    setOrders(orders.map(order => 
      order.id === orderId 
        ? { ...order, status: newStatus, updatedAt: new Date().toISOString() }
        : order
    ))
  }

  const handleExportOrders = () => {
    // ## Database: Implement order export functionality
    console.log("Export orders")
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('totalOrders')}</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <Package className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('totalRevenue')}</p>
                <p className="text-2xl font-bold">${stats.totalRevenue.toLocaleString()}</p>
              </div>
              <DollarSign className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('averageOrderValue')}</p>
                <p className="text-2xl font-bold">${stats.averageOrderValue}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('pendingOrders')}</p>
                <p className="text-2xl font-bold">{stats.pending}</p>
              </div>
              <Users className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Orders Management */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <CardTitle>{t('ordersManagement')}</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleExportOrders}>
                <Download className="w-4 h-4 mr-2" />
                {t('export')}
              </Button>
              <Button onClick={() => window.location.reload()}>
                <RefreshCw className="w-4 h-4 mr-2" />
                {t('refresh')}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder={t('search')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as OrderStatus | "all")}>
              <SelectTrigger>
                <SelectValue placeholder={t('allStatus')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('allStatus')}</SelectItem>
                <SelectItem value="pending">{t('pending')}</SelectItem>
                <SelectItem value="processing">{t('processing')}</SelectItem>
                <SelectItem value="completed">{t('completed')}</SelectItem>
                <SelectItem value="cancelled">{t('cancelled')}</SelectItem>
                <SelectItem value="refunded">{t('refunded')}</SelectItem>
              </SelectContent>
            </Select>

            <Select value={paymentFilter} onValueChange={(value) => setPaymentFilter(value as PaymentStatus | "all")}>
              <SelectTrigger>
                <SelectValue placeholder={t('paymentStatus')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{language === "en" ? "All Payments" : "جميع المدفوعات"}</SelectItem>
                <SelectItem value="pending">{t('pending')}</SelectItem>
                <SelectItem value="paid">{t('paid')}</SelectItem>
                <SelectItem value="failed">{t('failed')}</SelectItem>
                <SelectItem value="refunded">{t('refunded')}</SelectItem>
              </SelectContent>
            </Select>

            <div className="text-sm text-muted-foreground flex items-center">
              <Filter className="w-4 h-4 mr-2" />
              {language === "en"
                ? `${filteredOrders.length} of ${orders.length} orders`
                : `${filteredOrders.length} من ${orders.length} طلب`}
            </div>
          </div>

          {/* Orders Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === "en" ? "Order" : "الطلب"}</TableHead>
                  <TableHead>{t('customer')}</TableHead>
                  <TableHead>{t('items')}</TableHead>
                  <TableHead>{t('amount')}</TableHead>
                  <TableHead>{t('status')}</TableHead>
                  <TableHead>{language === "en" ? "Payment" : "الدفع"}</TableHead>
                  <TableHead>{t('date')}</TableHead>
                  <TableHead>{t('actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOrders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-medium">
                      <div>
                        <div className="font-semibold">#{order.orderNumber}</div>
                        <div className="text-sm text-muted-foreground">
                          {order.paymentTransactionId && `Txn: ${order.paymentTransactionId.slice(-8)}`}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{order.userName}</div>
                        <div className="text-sm text-muted-foreground">{order.userEmail}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {language === "en"
                          ? `${order.items.length} item${order.items.length !== 1 ? 's' : ''}`
                          : `${order.items.length} عنصر`}
                        <div className="text-xs text-muted-foreground">
                          {order.items[0]?.productName[language]}
                          {order.items.length > 1 && (language === "en"
                            ? ` +${order.items.length - 1} more`
                            : ` +${order.items.length - 1} أكثر`)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-semibold">${order.totalAmount}</div>
                      <div className="text-xs text-muted-foreground">{order.currency}</div>
                    </TableCell>
                    <TableCell>
                      <Select
                        value={order.status}
                        onValueChange={(value) => handleUpdateOrderStatus(order.id, value as OrderStatus)}
                      >
                        <SelectTrigger className="w-32">
                          <Badge className={`${getOrderStatusColor(order.status)} text-white border-0`}>
                            {t(order.status)}
                          </Badge>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pending">{t('pending')}</SelectItem>
                          <SelectItem value="processing">{t('processing')}</SelectItem>
                          <SelectItem value="completed">{t('completed')}</SelectItem>
                          <SelectItem value="cancelled">{t('cancelled')}</SelectItem>
                          <SelectItem value="refunded">{t('refunded')}</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>
                      <Badge className={`${getPaymentStatusColor(order.paymentStatus)} text-white`}>
                        {t(order.paymentStatus)}
                      </Badge>
                      <div className="text-xs text-muted-foreground mt-1 capitalize">
                        {t(order.paymentMethod.replace('_', ''))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(order.createdAt).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(order.createdAt).toLocaleTimeString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm" onClick={() => handleViewOrder(order)}>
                          <Eye className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Order Details Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {t('orderDetails')} - #{selectedOrder?.orderNumber}
            </DialogTitle>
          </DialogHeader>

          {selectedOrder && (
            <div className="space-y-6">
              {/* Order Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="font-semibold">{language === "en" ? "Customer Information" : "معلومات العميل"}</h3>
                  <div className="space-y-2">
                    <div><strong>{t('name')}:</strong> {selectedOrder.userName}</div>
                    <div><strong>{language === "en" ? "Email" : "البريد الإلكتروني"}:</strong> {selectedOrder.userEmail}</div>
                    <div><strong>{language === "en" ? "User ID" : "معرف المستخدم"}:</strong> {selectedOrder.userId}</div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold">{language === "en" ? "Order Information" : "معلومات الطلب"}</h3>
                  <div className="space-y-2">
                    <div><strong>{language === "en" ? "Order Date" : "تاريخ الطلب"}:</strong> {new Date(selectedOrder.createdAt).toLocaleString()}</div>
                    <div><strong>{t('paymentMethod')}:</strong> {t(selectedOrder.paymentMethod.replace('_', ''))}</div>
                    <div><strong>{language === "en" ? "Transaction ID" : "معرف المعاملة"}:</strong> {selectedOrder.paymentTransactionId || (language === "en" ? "N/A" : "غير متوفر")}</div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Order Items */}
              <div>
                <h3 className="font-semibold mb-4">{language === "en" ? "Order Items" : "عناصر الطلب"}</h3>
                <div className="space-y-3">
                  {selectedOrder.items.map((item) => (
                    <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium">{item.productName[language]}</h4>
                        <p className="text-sm text-muted-foreground">{item.productDescription[language]}</p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="outline">{t(item.productType)}</Badge>
                          <span className="text-sm">
                            {language === "en" ? "Qty:" : "الكمية:"} {item.quantity}
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">${item.totalPrice}</div>
                        <div className="text-sm text-muted-foreground">
                          ${item.unitPrice} {language === "en" ? "each" : "لكل واحد"}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Order Summary */}
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>{t('subtotal')}:</span>
                  <span>${selectedOrder.subtotal}</span>
                </div>
                {selectedOrder.taxAmount > 0 && (
                  <div className="flex justify-between">
                    <span>{t('tax')}:</span>
                    <span>${selectedOrder.taxAmount}</span>
                  </div>
                )}
                {selectedOrder.discountAmount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>{t('discount')}:</span>
                    <span>-${selectedOrder.discountAmount}</span>
                  </div>
                )}
                <Separator />
                <div className="flex justify-between text-lg font-semibold">
                  <span>{t('total')}:</span>
                  <span>${selectedOrder.totalAmount}</span>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
