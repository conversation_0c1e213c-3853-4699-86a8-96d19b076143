import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock, User, ArrowRight } from "lucide-react"

export const metadata: Metadata = {
  title: "RNG Bypass Guides - Complete Tutorials for PUBG Mobile Emulator Bypass | RNG STORE",
  description: "Learn how to use RNG bypass with our comprehensive guides. Step-by-step tutorials for PUBG mobile emulator bypass, setup guides, and troubleshooting tips.",
  keywords: "RNG bypass guide, PUBG mobile emulator bypass tutorial, how to use RNG bypass, emulator bypass setup, PUBG mobile PC guide"
}

const guides = [
  {
    id: 1,
    title: "Complete RNG Bypass Setup Guide 2024",
    description: "Step-by-step tutorial on how to install and configure RNG bypass for PUBG Mobile emulator detection bypass.",
    content: "Learn the complete process of setting up RNG bypass for PUBG Mobile. This comprehensive guide covers installation, configuration, and optimization for all major emulators including BlueStacks, GameLoop, and NoxPlayer.",
    category: "Setup",
    readTime: "8 min read",
    author: "RNG Expert",
    date: "2024-01-20",
    featured: true,
    slug: "complete-rng-bypass-setup-guide-2024"
  },
  {
    id: 2,
    title: "How to Play PUBG Mobile on PC Undetected",
    description: "Ultimate guide to playing PUBG Mobile on PC emulators without getting detected or banned.",
    content: "Discover the secrets to playing PUBG Mobile on PC safely. Learn about emulator detection methods, how RNG bypass works, and best practices for staying undetected.",
    category: "Tutorial",
    readTime: "12 min read",
    author: "Gaming Pro",
    date: "2024-01-18",
    featured: true,
    slug: "how-to-play-pubg-mobile-pc-undetected"
  },
  {
    id: 3,
    title: "RNG Bypass vs Other Emulator Methods",
    description: "Compare RNG bypass with other emulator bypass methods and understand why it's the best choice.",
    content: "Comprehensive comparison of different emulator bypass techniques. Learn why RNG bypass is superior to other methods in terms of safety, reliability, and performance.",
    category: "Comparison",
    readTime: "6 min read",
    author: "Tech Analyst",
    date: "2024-01-15",
    featured: false,
    slug: "rng-bypass-vs-other-emulator-methods"
  },
  {
    id: 4,
    title: "Troubleshooting RNG Bypass Issues",
    description: "Common problems and solutions for RNG bypass not working or connection issues.",
    content: "Solve common RNG bypass problems with our troubleshooting guide. Covers installation issues, connection problems, and compatibility fixes.",
    category: "Support",
    readTime: "5 min read",
    author: "Support Team",
    date: "2024-01-12",
    featured: false,
    slug: "troubleshooting-rng-bypass-issues"
  },
  {
    id: 5,
    title: "Best Emulators for PUBG Mobile with RNG Bypass",
    description: "Discover which Android emulators work best with RNG bypass for optimal PUBG Mobile performance.",
    content: "Compare the top Android emulators for PUBG Mobile gaming. Learn which emulators offer the best performance, compatibility, and features when used with RNG bypass.",
    category: "Reviews",
    readTime: "10 min read",
    author: "Gaming Expert",
    date: "2024-01-10",
    featured: false,
    slug: "best-emulators-pubg-mobile-rng-bypass"
  },
  {
    id: 6,
    title: "RNG Bypass Safety and Security Guide",
    description: "Learn how to use RNG bypass safely and protect your PUBG Mobile account from bans.",
    content: "Essential safety tips for using RNG bypass. Learn about account protection, safe gaming practices, and how to minimize risks while using emulator bypass tools.",
    category: "Safety",
    readTime: "7 min read",
    author: "Security Expert",
    date: "2024-01-08",
    featured: false,
    slug: "rng-bypass-safety-security-guide"
  }
]

const categories = ["All", "Setup", "Tutorial", "Comparison", "Support", "Reviews", "Safety"]

export default function GuidesPage() {
  const featuredGuides = guides.filter(guide => guide.featured)
  const regularGuides = guides.filter(guide => !guide.featured)

  return (
    <div className="min-h-screen bg-zinc-900 text-white">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            RNG Bypass <span className="text-orange-400">Guides</span>
          </h1>
          <p className="text-xl text-zinc-300 max-w-3xl mx-auto">
            Master RNG bypass with our comprehensive guides and tutorials. 
            Learn everything from basic setup to advanced techniques for PUBG Mobile emulator bypass.
          </p>
        </div>

        {/* Categories */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {categories.map((category) => (
            <Badge
              key={category}
              variant={category === "All" ? "default" : "outline"}
              className={`cursor-pointer transition-colors ${
                category === "All" 
                  ? "bg-orange-500 text-white" 
                  : "border-zinc-600 text-zinc-300 hover:border-orange-400 hover:text-orange-400"
              }`}
            >
              {category}
            </Badge>
          ))}
        </div>

        {/* Featured Guides */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-6 flex items-center">
            <span className="text-orange-400">Featured</span>
            <span className="ml-2">Guides</span>
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {featuredGuides.map((guide) => (
              <Card key={guide.id} className="bg-zinc-800 border-zinc-700 hover:border-orange-400 transition-colors group">
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <Badge className="bg-orange-500 text-white">Featured</Badge>
                    <Badge variant="outline" className="border-zinc-600 text-zinc-400">
                      {guide.category}
                    </Badge>
                  </div>
                  <CardTitle className="group-hover:text-orange-400 transition-colors">
                    {guide.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-zinc-300 mb-4 leading-relaxed">
                    {guide.description}
                  </p>
                  <p className="text-zinc-400 text-sm mb-4 line-clamp-2">
                    {guide.content}
                  </p>
                  <div className="flex items-center justify-between text-sm text-zinc-500 mb-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        <User className="w-4 h-4 mr-1" />
                        {guide.author}
                      </div>
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {guide.readTime}
                      </div>
                    </div>
                    <span>{new Date(guide.date).toLocaleDateString()}</span>
                  </div>
                  <Link
                    href={`/guides/${guide.slug}`}
                    className="inline-flex items-center text-orange-400 hover:text-orange-300 transition-colors"
                  >
                    Read Guide
                    <ArrowRight className="w-4 h-4 ml-1" />
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* All Guides */}
        <div>
          <h2 className="text-2xl font-bold mb-6">All Guides</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {regularGuides.map((guide) => (
              <Card key={guide.id} className="bg-zinc-800 border-zinc-700 hover:border-orange-400 transition-colors group">
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="outline" className="border-zinc-600 text-zinc-400">
                      {guide.category}
                    </Badge>
                    <span className="text-xs text-zinc-500">{guide.readTime}</span>
                  </div>
                  <CardTitle className="text-lg group-hover:text-orange-400 transition-colors">
                    {guide.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-zinc-300 text-sm mb-4 leading-relaxed">
                    {guide.description}
                  </p>
                  <div className="flex items-center justify-between text-xs text-zinc-500 mb-4">
                    <div className="flex items-center">
                      <User className="w-3 h-3 mr-1" />
                      {guide.author}
                    </div>
                    <span>{new Date(guide.date).toLocaleDateString()}</span>
                  </div>
                  <Link
                    href={`/guides/${guide.slug}`}
                    className="inline-flex items-center text-orange-400 hover:text-orange-300 transition-colors text-sm"
                  >
                    Read Guide
                    <ArrowRight className="w-4 h-4 ml-1" />
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 border-0 mt-12">
          <CardContent className="text-center py-8">
            <h2 className="text-2xl font-bold text-white mb-4">
              Ready to Try RNG Bypass?
            </h2>
            <p className="text-orange-100 mb-6">
              Get started with our professional RNG bypass tools and play PUBG Mobile on PC undetected.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/hacks"
                className="bg-white text-orange-600 px-6 py-3 rounded-lg font-semibold hover:bg-orange-50 transition-colors"
              >
                Browse RNG Bypass Tools
              </Link>
              <Link
                href="/faq"
                className="bg-orange-700 text-white px-6 py-3 rounded-lg font-semibold hover:bg-orange-800 transition-colors"
              >
                View FAQ
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
