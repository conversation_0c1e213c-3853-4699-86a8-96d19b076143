@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Mobile-optimized text truncation */
  .text-truncate-mobile {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* Ensure consistent card heights */
  .card-content-height {
    min-height: 120px;
  }

  /* Mobile touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}

@layer base {
  :root {
    /* Core theme colors */
    --background: #18181b;
    --foreground: #ffffff;
    --card: #000000;
    --card-foreground: #ffffff;
    --popover: #18181b;
    --popover-foreground: #ffffff;

    /* Brand colors */
    --primary: #f97316;
    --primary-foreground: #000000;
    --accent: #f97316;
    --accent-foreground: #ffffff;

    /* UI colors */
    --secondary: #232329;
    --secondary-foreground: #ffffff;
    --muted: #a1a1aa;
    --muted-foreground: #d4d4d8;
    --destructive: #dc2626;
    --destructive-foreground: #ffffff;

    /* Interactive colors */
    --border: #27272a;
    --input: #232329;
    --ring: #f97316;

    /* Status colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* Common surface colors */
    --gray-900: #111827;
    --gray-800: #1f2937;
    --gray-700: #374151;
    --gray-600: #4b5563;
    --gray-500: #6b7280;
    --gray-400: #9ca3af;
    --gray-300: #d1d5db;

    --zinc-900: #18181b;
    --zinc-800: #27272a;
    --zinc-700: #3f3f46;
    --zinc-600: #52525b;
    --zinc-500: #71717a;
    --zinc-400: #a1a1aa;
    --zinc-300: #d4d4d8;

    /* Orange scale */
    --orange-600: #ea580c;
    --orange-500: #f97316;
    --orange-400: #fb923c;

    /* Status variants */
    --green-500: #10b981;
    --red-500: #ef4444;
    --blue-400: #60a5fa;
    --purple-400: #c084fc;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-black;
}

::-webkit-scrollbar-thumb {
  @apply bg-zinc-700 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-zinc-600;
}

/* Smooth animations */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Arabic font support */
.font-cairo {
  font-family: var(--font-cairo), sans-serif;
}

.font-inter {
  font-family: var(--font-inter), sans-serif;
}

/* RTL support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .space-x-2 > * + * {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .space-x-4 > * + * {
  margin-left: 0;
  margin-right: 1rem;
}

/* Loading animations */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Gradient backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #ea580c 100%);
}

/* Card hover effects */
.card-hover {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Reduce hover effects on mobile for better performance */
  .card-hover:hover {
    transform: none;
    box-shadow: none;
  }

  /* Ensure proper image aspect ratios on mobile */
  .aspect-square {
    aspect-ratio: 1 / 1;
  }

  /* Better spacing for mobile cards */
  .mobile-card-spacing {
    padding: 0.5rem;
  }

  /* Improved button sizing for mobile */
  .mobile-button {
    min-height: 36px;
    font-size: 0.75rem;
  }
}

/* Improved responsive image handling */
.responsive-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  transition: transform 0.3s ease;
}

/* Better text handling for RTL languages */
[dir="rtl"] .text-truncate-mobile {
  text-align: right;
}

/* Ensure consistent card grid alignment */
.card-grid {
  display: grid;
  gap: 0.5rem;
}

@media (min-width: 640px) {
  .card-grid {
    gap: 1rem;
  }
}

@media (min-width: 768px) {
  .card-grid {
    gap: 1.5rem;
  }
}
