import type { <PERSON><PERSON><PERSON> } from "next"
import { notFound } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Star, Shield, Download, Users, Clock, CheckCircle } from "lucide-react"

// Mock data for individual hack pages
const getHackById = (id: string) => {
  const hacks = [
    {
      id: "1",
      name: {
        en: "RNG Bypass Pro - PUBG Mobile Emulator Hack 2024",
        ar: "RNG تجاوز برو - هاك محاكي ببجي موبايل 2024"
      },
      slug: "rng-bypass-pro-pubg-mobile-emulator-hack",
      price: 79.99,
      originalPrice: 129.99,
      description: {
        en: "Professional RNG bypass tool for PUBG mobile emulator detection. Play PUBG mobile on PC undetected with advanced Android emulator bypass technology. Features anti-detection algorithms, automatic updates, and 24/7 support. Compatible with BlueStacks, GameLoop, and all major Android emulators. Safe, undetected RNG hack with 99.9% success rate.",
        ar: "أداة تجاوز RNG احترافية لكشف محاكي ببجي موبايل. العب ببجي موبايل على الكمبيوتر دون كشف مع تقنية تجاوز المحاكي الاندرويد المتقدمة."
      },
      features: [
        "Advanced RNG bypass technology",
        "Compatible with all major emulators",
        "Anti-detection algorithms",
        "Automatic updates",
        "24/7 customer support",
        "99.9% success rate",
        "Safe and undetected",
        "Easy installation"
      ],
      compatibility: ["BlueStacks", "GameLoop", "NoxPlayer", "LDPlayer", "MEmu"],
      version: "3.2.1",
      lastUpdated: "2024-01-20",
      downloadCount: 2850,
      rating: 4.9,
      reviews: 156,
      status: "active",
      imageUrl: "/mido-logo.jpg"
    }
  ]
  
  return hacks.find(hack => hack.id === id)
}

export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const hack = getHackById(params.id)
  
  if (!hack) {
    return {
      title: "Hack Not Found | RNG STORE"
    }
  }

  return {
    title: `${hack.name.en} - Download RNG Bypass | RNG STORE`,
    description: `Download ${hack.name.en} for $${hack.price}. ${hack.description.en.substring(0, 150)}...`,
    keywords: "RNG bypass download, PUBG mobile emulator bypass, Android emulator hack, RNG hack, PUBG mobile PC hack",
    openGraph: {
      title: hack.name.en,
      description: hack.description.en,
      images: [hack.imageUrl],
      type: "product"
    }
  }
}

export default function HackDetailPage({ params }: { params: { id: string } }) {
  const hack = getHackById(params.id)

  if (!hack) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-zinc-900 text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="mb-6 text-sm">
          <span className="text-zinc-400">Home</span>
          <span className="mx-2 text-zinc-600">/</span>
          <span className="text-zinc-400">Hacks</span>
          <span className="mx-2 text-zinc-600">/</span>
          <span className="text-orange-400">{hack.name.en}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Product Image */}
          <div className="space-y-4">
            <div className="aspect-square bg-zinc-800 rounded-lg overflow-hidden">
              <img
                src={hack.imageUrl}
                alt={hack.name.en}
                className="w-full h-full object-cover"
              />
            </div>
            
            {/* Trust Badges */}
            <div className="grid grid-cols-3 gap-4">
              <Card className="bg-zinc-800 border-zinc-700 text-center p-4">
                <Shield className="w-6 h-6 text-green-400 mx-auto mb-2" />
                <p className="text-xs text-zinc-300">Safe & Secure</p>
              </Card>
              <Card className="bg-zinc-800 border-zinc-700 text-center p-4">
                <Download className="w-6 h-6 text-blue-400 mx-auto mb-2" />
                <p className="text-xs text-zinc-300">{hack.downloadCount}+ Downloads</p>
              </Card>
              <Card className="bg-zinc-800 border-zinc-700 text-center p-4">
                <Users className="w-6 h-6 text-purple-400 mx-auto mb-2" />
                <p className="text-xs text-zinc-300">{hack.reviews} Reviews</p>
              </Card>
            </div>
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Badge className="bg-orange-500 text-white">
                  <Star className="w-3 h-3 mr-1" />
                  Featured
                </Badge>
                <Badge variant="outline" className="text-green-400 border-green-400">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  {hack.status === "active" ? "Available" : "Sold Out"}
                </Badge>
              </div>
              
              <h1 className="text-3xl font-bold mb-4">{hack.name.en}</h1>
              
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${i < Math.floor(hack.rating) ? 'text-yellow-400 fill-current' : 'text-zinc-600'}`}
                    />
                  ))}
                  <span className="ml-2 text-sm text-zinc-400">
                    {hack.rating} ({hack.reviews} reviews)
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-4 mb-6">
                <span className="text-3xl font-bold text-orange-400">${hack.price}</span>
                {hack.originalPrice && (
                  <span className="text-xl text-zinc-500 line-through">${hack.originalPrice}</span>
                )}
                <Badge className="bg-red-500 text-white">
                  Save ${(hack.originalPrice || 0) - hack.price}
                </Badge>
              </div>

              <p className="text-zinc-300 mb-6 leading-relaxed">
                {hack.description.en}
              </p>

              <Button 
                size="lg" 
                className="w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-3"
              >
                <Download className="w-5 h-5 mr-2" />
                Download RNG Bypass Now - ${hack.price}
              </Button>

              <div className="flex items-center justify-center gap-4 mt-4 text-sm text-zinc-400">
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  Instant Download
                </div>
                <div className="flex items-center">
                  <Shield className="w-4 h-4 mr-1" />
                  30-Day Guarantee
                </div>
              </div>
            </div>

            {/* Product Info */}
            <Card className="bg-zinc-800 border-zinc-700">
              <CardHeader>
                <CardTitle className="text-lg">Product Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-zinc-400">Version:</span>
                  <span>{hack.version}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-zinc-400">Last Updated:</span>
                  <span>{new Date(hack.lastUpdated).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-zinc-400">Downloads:</span>
                  <span>{hack.downloadCount.toLocaleString()}+</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-zinc-400">Support:</span>
                  <span className="text-green-400">24/7 Available</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Features Section */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {hack.features.map((feature, index) => (
              <Card key={index} className="bg-zinc-800 border-zinc-700 p-4">
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-400 mr-3 flex-shrink-0" />
                  <span className="text-sm">{feature}</span>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Compatibility */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">Emulator Compatibility</h2>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {hack.compatibility.map((emulator, index) => (
              <Card key={index} className="bg-zinc-800 border-zinc-700 p-4 text-center">
                <CheckCircle className="w-6 h-6 text-green-400 mx-auto mb-2" />
                <span className="text-sm font-medium">{emulator}</span>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
