/**
 * Account Management Types
 * 
 * ## Database Schema Notes:
 * - All bilingual fields should be stored as JSON objects with 'en' and 'ar' keys
 * - Price should be stored as decimal(10,2) in database
 * - Created/updated timestamps should be automatically managed
 * - Status field for soft delete functionality
 */

// Bilingual text interface for database translations
export interface BilingualText {
  en: string;
  ar: string;
}

// Main account interface for the new simplified structure
export interface Account {
  id: string;
  
  // ## Database field: account_name (JSON) - Bilingual account name/type
  name: BilingualText;
  
  // ## Database field: description (JSON) - Bilingual description
  description: BilingualText;
  
  // ## Database field: price (DECIMAL(10,2)) - Account price in USD
  price: number;
  
  // ## Database field: status (ENUM) - active, inactive, sold
  status: 'active' | 'inactive' | 'sold';
  
  // ## Database field: created_at (TIMESTAMP)
  createdAt: string;
  
  // ## Database field: updated_at (TIMESTAMP)
  updatedAt: string;
  
  // ## Database field: image_url (VARCHAR) - Optional account image
  imageUrl?: string;
  
  // ## Database field: is_featured (BOOLEAN) - For homepage display
  isFeatured: boolean;
}

// Form data interface for creating/editing accounts
export interface AccountFormData {
  name: BilingualText;
  description: BilingualText;
  price: number;
  imageUrl?: string;
  isFeatured: boolean;
}

// Interface for account creation/update API requests
export interface CreateAccountRequest {
  name: BilingualText;
  description: BilingualText;
  price: number;
  imageUrl?: string;
  isFeatured?: boolean;
}

export interface UpdateAccountRequest extends Partial<CreateAccountRequest> {
  id: string;
}

// Account list response for admin management
export interface AccountListResponse {
  accounts: Account[];
  total: number;
  page: number;
  limit: number;
}

// Account filters for admin search/filter functionality
export interface AccountFilters {
  status?: Account['status'];
  priceMin?: number;
  priceMax?: number;
  search?: string; // Search in name and description
  isFeatured?: boolean;
}

// Account statistics for admin dashboard
export interface AccountStats {
  total: number;
  active: number;
  inactive: number;
  sold: number;
  totalRevenue: number;
  averagePrice: number;
}

// Default empty bilingual text
export const createEmptyBilingualText = (): BilingualText => ({
  en: '',
  ar: ''
});

// Default account form data
export const createDefaultAccountFormData = (): AccountFormData => ({
  name: createEmptyBilingualText(),
  description: createEmptyBilingualText(),
  price: 0,
  imageUrl: '',
  isFeatured: false
});

// Validation helpers
export const validateBilingualText = (text: BilingualText): boolean => {
  return text.en.trim().length > 0 && text.ar.trim().length > 0;
};

export const validateAccountFormData = (data: AccountFormData): string[] => {
  const errors: string[] = [];

  if (!validateBilingualText(data.name)) {
    errors.push('Account name is required in both English and Arabic');
  }

  if (!validateBilingualText(data.description)) {
    errors.push('Description is required in both English and Arabic');
  }

  if (data.price <= 0) {
    errors.push('Price must be greater than 0');
  }

  return errors;
};
