"use client"

import { motion } from "framer-motion"

interface PageHeaderProps {
  title: string
  description?: string
}

export function PageHeader({ title, description }: PageHeaderProps) {
  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-12">
      <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">{title}</h1>
      {description && <p className="text-zinc-400 text-lg max-w-2xl mx-auto">{description}</p>}
    </motion.div>
  )
}
