"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ImageUpload } from "@/components/ui/image-upload"
import { Save, X } from "lucide-react"
import { AccountFormData, createDefaultAccountFormData, validateAccountFormData } from "@/types/account"

/**
 * Account Form Component
 * 
 * ## Database Integration Notes:
 * - This form will handle both create and edit operations
 * - Implement image upload functionality for account images
 * - Add form validation with real-time feedback
 * - Connect to Supabase accounts table for CRUD operations
 */

interface AccountFormProps {
  account?: AccountFormData
  onSave: (data: AccountFormData) => void
  onCancel: () => void
  isLoading?: boolean
}

export function AccountForm({ account, onSave, onCancel, isLoading = false }: AccountFormProps) {
  const [formData, setFormData] = useState<AccountFormData>(
    account || createDefaultAccountFormData()
  )
  const [errors, setErrors] = useState<string[]>([])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form data
    const validationErrors = validateAccountFormData(formData)
    if (validationErrors.length > 0) {
      setErrors(validationErrors)
      return
    }
    
    setErrors([])
    onSave(formData)
  }

  const updateBilingualField = (
    field: 'name' | 'description',
    language: 'en' | 'ar',
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: {
        ...prev[field],
        [language]: value
      }
    }))
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>
          {account ? "Edit Account" : "Create New Account"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Display */}
          {errors.length > 0 && (
            <div className="bg-red-500/10 border border-red-500 rounded-lg p-4">
              <h4 className="text-red-500 font-medium mb-2">Please fix the following errors:</h4>
              <ul className="text-red-400 text-sm space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Bilingual Fields */}
          <Tabs defaultValue="en" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="en">English</TabsTrigger>
              <TabsTrigger value="ar">العربية</TabsTrigger>
            </TabsList>
            
            <TabsContent value="en" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name-en">Account Name (English)</Label>
                <Input
                  id="name-en"
                  value={formData.name.en}
                  onChange={(e) => updateBilingualField('name', 'en', e.target.value)}
                  placeholder="e.g., Premium PUBG Mobile Account"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description-en">Description (English)</Label>
                <Textarea
                  id="description-en"
                  value={formData.description.en}
                  onChange={(e) => updateBilingualField('description', 'en', e.target.value)}
                  placeholder="Detailed description of the account..."
                  rows={4}
                  required
                />
              </div>
            </TabsContent>
            
            <TabsContent value="ar" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name-ar">اسم الحساب (العربية)</Label>
                <Input
                  id="name-ar"
                  value={formData.name.ar}
                  onChange={(e) => updateBilingualField('name', 'ar', e.target.value)}
                  placeholder="مثال: حساب PUBG موبايل مميز"
                  dir="rtl"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description-ar">الوصف (العربية)</Label>
                <Textarea
                  id="description-ar"
                  value={formData.description.ar}
                  onChange={(e) => updateBilingualField('description', 'ar', e.target.value)}
                  placeholder="وصف مفصل للحساب..."
                  rows={4}
                  dir="rtl"
                  required
                />
              </div>
            </TabsContent>
          </Tabs>

          {/* Price */}
          <div className="space-y-2">
            <Label htmlFor="price">Price (USD)</Label>
            <Input
              id="price"
              type="number"
              step="0.01"
              min="0"
              value={formData.price}
              onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
              placeholder="0.00"
              required
            />
          </div>

          {/* Image Upload */}
          <div className="space-y-2">
            <Label>Account Image</Label>
            <ImageUpload
              value={formData.imageUrl || ''}
              onChange={(url) => setFormData(prev => ({ ...prev, imageUrl: url }))}
              onRemove={() => setFormData(prev => ({ ...prev, imageUrl: '' }))}
              disabled={isLoading}
              maxWidth={800}
              maxHeight={600}
              quality={0.8}
            />
          </div>

          {/* Featured Toggle */}
          <div className="flex items-center space-x-2">
            <Switch
              id="featured"
              checked={formData.isFeatured}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isFeatured: checked }))}
            />
            <Label htmlFor="featured">Featured Account (Display on homepage)</Label>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-orange-500 hover:bg-orange-600"
            >
              <Save className="w-4 h-4 mr-2" />
              {isLoading ? "Saving..." : "Save Account"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
