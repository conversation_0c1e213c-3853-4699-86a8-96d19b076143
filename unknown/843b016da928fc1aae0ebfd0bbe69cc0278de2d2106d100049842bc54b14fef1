"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ImageUpload } from "@/components/ui/image-upload"
import { Save, X } from "lucide-react"
import { 
  HackFormData, 
  createDefaultHackFormData, 
  validateHackFormData,
  HackType,
  Platform,
  RootStatus,
  JailbreakStatus
} from "@/types/hack"

/**
 * Hack Form Component
 * 
 * ## Database Integration Notes:
 * - This form handles conditional field validation based on hack type
 * - Implement file upload functionality for hack files
 * - Add real-time validation with conditional field requirements
 * - Connect to Supabase hacks table for CRUD operations
 * - Support multi-company versions of the same hack
 */

interface HackFormProps {
  hack?: HackFormData
  onSave: (data: HackFormData) => void
  onCancel: () => void
  isLoading?: boolean
}

export function HackForm({ hack, onSave, onCancel, isLoading = false }: HackFormProps) {
  const [formData, setFormData] = useState<HackFormData>(
    hack || createDefaultHackFormData()
  )
  const [errors, setErrors] = useState<string[]>([])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form data with conditional requirements
    const validationErrors = validateHackFormData(formData)
    if (validationErrors.length > 0) {
      setErrors(validationErrors)
      return
    }
    
    setErrors([])
    onSave(formData)
  }

  const updateBilingualField = (
    field: 'name' | 'description',
    language: 'en' | 'ar',
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: {
        ...prev[field],
        [language]: value
      }
    }))
  }

  const handleTypeChange = (type: HackType) => {
    setFormData(prev => ({
      ...prev,
      type,
      // Reset platform-specific fields when type changes
      platform: type === 'pc' ? undefined : prev.platform,
      rootStatus: undefined,
      jailbreakStatus: undefined
    }))
  }

  const handlePlatformChange = (platform: Platform | 'none') => {
    setFormData(prev => ({
      ...prev,
      platform: platform === 'none' ? undefined : platform as Platform,
      // Reset platform-specific fields when platform changes
      rootStatus: platform === 'android' ? prev.rootStatus : undefined,
      jailbreakStatus: platform === 'ios' ? prev.jailbreakStatus : undefined
    }))
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>
          {hack ? "Edit Hack" : "Create New Hack"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Display */}
          {errors.length > 0 && (
            <div className="bg-red-500/10 border border-red-500 rounded-lg p-4">
              <h4 className="text-red-500 font-medium mb-2">Please fix the following errors:</h4>
              <ul className="text-red-400 text-sm space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Bilingual Fields */}
          <Tabs defaultValue="en" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="en">English</TabsTrigger>
              <TabsTrigger value="ar">العربية</TabsTrigger>
            </TabsList>
            
            <TabsContent value="en" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name-en">Hack Name (English)</Label>
                <Input
                  id="name-en"
                  value={formData.name.en}
                  onChange={(e) => updateBilingualField('name', 'en', e.target.value)}
                  placeholder="e.g., PUBG Mobile Aimbot Pro"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description-en">Description (English)</Label>
                <Textarea
                  id="description-en"
                  value={formData.description.en}
                  onChange={(e) => updateBilingualField('description', 'en', e.target.value)}
                  placeholder="Detailed description of the hack features..."
                  rows={4}
                  required
                />
              </div>
            </TabsContent>
            
            <TabsContent value="ar" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name-ar">اسم الهاك (العربية)</Label>
                <Input
                  id="name-ar"
                  value={formData.name.ar}
                  onChange={(e) => updateBilingualField('name', 'ar', e.target.value)}
                  placeholder="مثال: ايمبوت PUBG موبايل برو"
                  dir="rtl"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description-ar">الوصف (العربية)</Label>
                <Textarea
                  id="description-ar"
                  value={formData.description.ar}
                  onChange={(e) => updateBilingualField('description', 'ar', e.target.value)}
                  placeholder="وصف مفصل لميزات الهاك..."
                  rows={4}
                  dir="rtl"
                  required
                />
              </div>
            </TabsContent>
          </Tabs>

          {/* Hack Type Selection */}
          <div className="space-y-2">
            <Label htmlFor="type">Hack Type</Label>
            <Select value={formData.type} onValueChange={handleTypeChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select hack type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mobile">Mobile</SelectItem>
                <SelectItem value="pc">PC</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Conditional Platform Selection for Mobile */}
          {formData.type === 'mobile' && (
            <div className="space-y-2">
              <Label htmlFor="platform">Platform</Label>
              <Select value={formData.platform || 'none'} onValueChange={handlePlatformChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select platform" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Select platform</SelectItem>
                  <SelectItem value="android">Android</SelectItem>
                  <SelectItem value="ios">iOS</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Conditional Root Status for Android */}
          {formData.type === 'mobile' && formData.platform === 'android' && (
            <div className="space-y-2">
              <Label htmlFor="rootStatus">Root Status</Label>
              <Select
                value={formData.rootStatus || 'none'}
                onValueChange={(value: RootStatus | 'none') =>
                  setFormData(prev => ({ ...prev, rootStatus: value === 'none' ? undefined : value as RootStatus }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select root status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Select root status</SelectItem>
                  <SelectItem value="root">Root Required</SelectItem>
                  <SelectItem value="non-root">Non-root</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Conditional Jailbreak Status for iOS */}
          {formData.type === 'mobile' && formData.platform === 'ios' && (
            <div className="space-y-2">
              <Label htmlFor="jailbreakStatus">Jailbreak Status</Label>
              <Select
                value={formData.jailbreakStatus || 'none'}
                onValueChange={(value: JailbreakStatus | 'none') =>
                  setFormData(prev => ({ ...prev, jailbreakStatus: value === 'none' ? undefined : value as JailbreakStatus }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select jailbreak status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Select jailbreak status</SelectItem>
                  <SelectItem value="jailbroken">Jailbroken Required</SelectItem>
                  <SelectItem value="non-jailbroken">Non-jailbroken</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Company and Version */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="company">Company/Developer</Label>
              <Input
                id="company"
                value={formData.company}
                onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                placeholder="e.g., GameHacks Inc"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="version">Version</Label>
              <Input
                id="version"
                value={formData.version}
                onChange={(e) => setFormData(prev => ({ ...prev, version: e.target.value }))}
                placeholder="e.g., 2.1.5"
                required
              />
            </div>
          </div>

          {/* Price */}
          <div className="space-y-2">
            <Label htmlFor="price">Price (USD)</Label>
            <Input
              id="price"
              type="number"
              step="0.01"
              min="0"
              value={formData.price}
              onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
              placeholder="0.00"
              required
            />
          </div>

          {/* Image Upload */}
          <div className="space-y-2">
            <Label>Hack Image</Label>
            <ImageUpload
              value={formData.imageUrl || ''}
              onChange={(url) => setFormData(prev => ({ ...prev, imageUrl: url }))}
              onRemove={() => setFormData(prev => ({ ...prev, imageUrl: '' }))}
              disabled={isLoading}
              maxWidth={800}
              maxHeight={600}
              quality={0.8}
            />
          </div>

          {/* Featured Toggle */}
          <div className="flex items-center space-x-2">
            <Switch
              id="featured"
              checked={formData.isFeatured}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isFeatured: checked }))}
            />
            <Label htmlFor="featured">Featured Hack (Display on homepage)</Label>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-orange-500 hover:bg-orange-600"
            >
              <Save className="w-4 h-4 mr-2" />
              {isLoading ? "Saving..." : "Save Hack"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
