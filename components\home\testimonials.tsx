"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { useLanguage } from "@/components/providers/language-provider"

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    text: "Best PUBG account shop I've ever used! Instant delivery and amazing support.",
    avatar: "/mido-logo.jpg",
  },
  {
    id: 2,
    name: "<PERSON>ima <PERSON>.",
    text: "The gaming tools are truly safe and boosted my performance significantly.",
    avatar: "/mido-logo.jpg",
  },
  {
    id: 3,
    name: "<PERSON>",
    text: "Received a level-150 account within minutes. Highly recommended.",
    avatar: "/mido-logo.jpg",
  },
]

export function Testimonials() {
  const { language } = useLanguage()

  return (
    <section className="py-20 bg-zinc-900">
      <div className="container mx-auto px-4">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-3xl md:text-4xl font-bold text-white text-center mb-12"
        >
          {language === "en" ? "What Gamers Say" : "آراء اللاعبين"}
        </motion.h2>

        <div className="grid grid-cols-3 gap-3 sm:gap-8">
          {testimonials.map((t) => (
            <motion.div
              key={t.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="bg-black border border-zinc-700 p-3 sm:p-6 rounded-lg text-center"
            >
              <Image
                src={t.avatar || "/mido-logo.jpg"}
                alt={t.name}
                width={60}
                height={60}
                className="rounded-full mx-auto mb-2 sm:mb-4 object-cover w-12 h-12 sm:w-20 sm:h-20"
              />
              <p className="text-zinc-300 text-xs sm:text-sm mb-2 sm:mb-4 leading-relaxed">
                “{language === "en" ? t.text : t.text /* could supply Arabic here */}”
              </p>
              <span className="text-orange-400 font-semibold text-xs sm:text-base">{t.name}</span>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
