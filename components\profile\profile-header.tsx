"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, ShoppingBag, DollarSign } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"

interface User {
  id: string
  name: string
  email: string
  avatar: string
  joinDate: string
  totalOrders: number
  totalSpent: number
}

interface ProfileHeaderProps {
  user: User
}

export function ProfileHeader({ user }: ProfileHeaderProps) {
  const { language } = useLanguage()

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="space-y-6">
      <Card className="bg-black border-zinc-700">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row items-center md:items-start space-y-4 md:space-y-0 md:space-x-6">
            <Image
              src={user.avatar || "/mido-logo.jpg"}
              alt={user.name}
              width={100}
              height={100}
              className="rounded-full object-cover"
            />

            <div className="flex-1 text-center md:text-left">
              <h1 className="text-2xl font-bold text-white mb-2">{user.name}</h1>
              <p className="text-zinc-400 mb-4">{user.email}</p>

              <div className="flex flex-wrap justify-center md:justify-start gap-4">
                <div className="flex items-center space-x-2 text-zinc-300">
                  <Calendar className="w-4 h-4 text-orange-400" />
                  <span className="text-sm">
                    {language === "en" ? "Joined" : "انضم في"} {user.joinDate}
                  </span>
                </div>

                <div className="flex items-center space-x-2 text-zinc-300">
                  <ShoppingBag className="w-4 h-4 text-orange-400" />
                  <span className="text-sm">
                    {user.totalOrders} {language === "en" ? "Orders" : "طلبات"}
                  </span>
                </div>

                <div className="flex items-center space-x-2 text-zinc-300">
                  <DollarSign className="w-4 h-4 text-orange-400" />
                  <span className="text-sm">
                    ${user.totalSpent} {language === "en" ? "Spent" : "مُنفق"}
                  </span>
                </div>
              </div>
            </div>

            <Badge className="bg-orange-500 text-white">{language === "en" ? "Premium Member" : "عضو مميز"}</Badge>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
