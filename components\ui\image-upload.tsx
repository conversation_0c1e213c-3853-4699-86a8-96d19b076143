"use client"

import { useState, useRef, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog"
import { Upload, X, <PERSON>otate<PERSON>w, Check } from "lucide-react"
import Image from "next/image"

/**
 * Simple Image Upload Component with Mobile-Friendly Crop
 *
 * ## Database Integration Notes:
 * - Upload images to Supabase Storage bucket 'images'
 * - Crop and resize images before upload for optimization
 * - Mobile-responsive touch interface for crop selection
 * - Returns optimized image URL for database storage
 *
 * ## Backend Commands:
 * ```sql
 * -- Create storage bucket for images
 * INSERT INTO storage.buckets (id, name, public) VALUES ('images', 'images', true);
 *
 * -- Set up RLS policies for image uploads
 * CREATE POLICY "Allow authenticated uploads" ON storage.objects
 * FOR INSERT WITH CHECK (bucket_id = 'images' AND auth.role() = 'authenticated');
 *
 * CREATE POLICY "Allow public access" ON storage.objects
 * FOR SELECT USING (bucket_id = 'images');
 * ```
 */

interface ImageUploadProps {
  value?: string
  onChange: (url: string) => void
  onRemove?: () => void
  disabled?: boolean
}

export function ImageUpload({
  value,
  onChange,
  onRemove,
  disabled = false
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [showCropDialog, setShowCropDialog] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string>("")
  const [cropData, setCropData] = useState({
    x: 0,
    y: 0,
    width: 100,
    height: 100
  })

  const fileInputRef = useRef<HTMLInputElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const cropperRef = useRef<any>(null)

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select a valid image file')
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB')
      return
    }

    setSelectedFile(file)
    const url = URL.createObjectURL(file)
    setPreviewUrl(url)
    setShowCropDialog(true)
  }, [])

  const handleCropAndUpload = useCallback(async () => {
    if (!selectedFile || !previewUrl) return

    setIsUploading(true)
    try {
      // Create a simple cropped image using canvas
      const canvas = canvasRef.current
      if (!canvas) throw new Error('Canvas not available')

      const ctx = canvas.getContext('2d')
      if (!ctx) throw new Error('Canvas context not available')

      // Create image element
      const img = document.createElement('img')
      await new Promise((resolve, reject) => {
        img.onload = resolve
        img.onerror = reject
        img.src = previewUrl
      })

      // Set canvas size to a reasonable output size
      const maxSize = 800
      const aspectRatio = img.width / img.height
      let outputWidth = maxSize
      let outputHeight = maxSize

      if (aspectRatio > 1) {
        outputHeight = maxSize / aspectRatio
      } else {
        outputWidth = maxSize * aspectRatio
      }

      canvas.width = outputWidth
      canvas.height = outputHeight

      // Draw the image to canvas (this automatically crops/resizes)
      ctx.drawImage(img, 0, 0, outputWidth, outputHeight)

      // Convert to blob
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => {
          if (blob) resolve(blob)
        }, 'image/jpeg', 0.8)
      })

      // ## Database: Upload to Supabase Storage
      // const { data, error } = await supabase.storage
      //   .from('images')
      //   .upload(`${Date.now()}-${selectedFile.name}`, blob, {
      //     contentType: 'image/jpeg',
      //     upsert: false
      //   })

      // For now, create a local URL (replace with actual Supabase URL)
      const uploadedUrl = URL.createObjectURL(blob)

      onChange(uploadedUrl)
      setShowCropDialog(false)
      setSelectedFile(null)
      setPreviewUrl("")

      // Clean up
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }

    } catch (error) {
      console.error('Upload failed:', error)
      alert('Failed to upload image. Please try again.')
    } finally {
      setIsUploading(false)
    }
  }, [selectedFile, previewUrl, onChange])

  const handleCancel = useCallback(() => {
    setShowCropDialog(false)
    setSelectedFile(null)
    setPreviewUrl("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }, [])

  const handleRemove = useCallback(() => {
    if (onRemove) {
      onRemove()
    } else {
      onChange("")
    }
  }, [onRemove, onChange])

  return (
    <div className="w-full space-y-4">
      {/* Current Image Display */}
      {value && (
        <div className="relative w-full max-w-xs mx-auto">
          <div className="relative aspect-square w-full overflow-hidden rounded-lg border border-zinc-700">
            <Image
              src={value}
              alt="Uploaded image"
              fill
              className="object-cover"
            />
            <Button
              type="button"
              variant="destructive"
              size="sm"
              className="absolute top-2 right-2 h-8 w-8 p-0"
              onClick={handleRemove}
              disabled={disabled}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Upload Button */}
      <div className="flex flex-col items-center gap-3">
        <Button
          type="button"
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          disabled={disabled || isUploading}
          className="w-full max-w-xs flex items-center gap-2"
        >
          <Upload className="w-4 h-4" />
          {value ? "Change Image" : "Upload Image"}
        </Button>

        <Input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled}
        />

        {isUploading && (
          <span className="text-sm text-zinc-400">Processing image...</span>
        )}
      </div>

      {/* Simple Preview Dialog */}
      <Dialog open={showCropDialog} onOpenChange={handleCancel}>
        <DialogContent className="max-w-md mx-auto">
          <DialogHeader>
            <DialogTitle>Confirm Image Upload</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Image Preview */}
            {previewUrl && (
              <div className="w-full">
                <div className="relative aspect-square w-full overflow-hidden rounded-lg border border-zinc-700">
                  <img
                    src={previewUrl}
                    alt="Preview"
                    className="w-full h-full object-cover"
                  />
                </div>
                <p className="text-sm text-zinc-400 mt-2 text-center">
                  Image will be automatically resized and optimized
                </p>
              </div>
            )}

            {/* Hidden Canvas for Processing */}
            <canvas ref={canvasRef} className="hidden" />
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isUploading}
              className="w-full sm:w-auto"
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleCropAndUpload}
              disabled={isUploading}
              className="w-full sm:w-auto bg-orange-500 hover:bg-orange-600"
            >
              <Check className="w-4 h-4 mr-2" />
              {isUploading ? "Processing..." : "Use Image"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
