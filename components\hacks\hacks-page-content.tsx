"use client"

import { HacksGrid } from "./hacks-grid"
import { HacksFilters } from "./hacks-filters"
import { PageHeader } from "@/components/ui/page-header"
import { useLanguage } from "@/components/providers/language-provider"
import { Hack } from "@/types/hack"

interface HacksPageContentProps {
  hacks: Hack[]
}

export function HacksPageContent({ hacks }: HacksPageContentProps) {
  const { language } = useLanguage()

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <PageHeader
          title={language === "en" ? "Gaming Hacks" : "هاكات الألعاب"}
          description={language === "en" 
            ? "Professional hacks and utilities to enhance your PUBG experience"
            : "هاكات وأدوات احترافية لتحسين تجربة PUBG الخاصة بك"}
        />

        {/* Compact Filters Above Content */}
        <div className="mb-6">
          <HacksFilters />
        </div>

        {/* Main Content Grid */}
        <main>
          <HacksGrid hacks={hacks} />
        </main>
      </div>
    </div>
  )
}
