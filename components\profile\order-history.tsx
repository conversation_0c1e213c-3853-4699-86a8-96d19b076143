"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Eye, Download } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"

interface OrderItem {
  name: string
  price: number
  type: "account" | "hack"
}

interface Order {
  id: string
  date: string
  status: "completed" | "pending" | "cancelled"
  total: number
  items: OrderItem[]
}

interface OrderHistoryProps {
  orders: Order[]
}

export function OrderHistory({ orders }: OrderHistoryProps) {
  const { language } = useLanguage()

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-500"
      case "pending":
        return "bg-warning"
      case "cancelled":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusText = (status: string) => {
    if (language === "en") {
      return status.charAt(0).toUpperCase() + status.slice(1)
    }

    switch (status) {
      case "completed":
        return "مكتمل"
      case "pending":
        return "قيد الانتظار"
      case "cancelled":
        return "ملغي"
      default:
        return status
    }
  }

  return (
    <Card className="bg-gray-900 border-zinc-700">
      <CardHeader>
        <CardTitle className="text-white">{language === "en" ? "Order History" : "تاريخ الطلبات"}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {orders.length === 0 ? (
          <p className="text-gray-400 text-center py-8">{language === "en" ? "No orders found" : "لا توجد طلبات"}</p>
        ) : (
          orders.map((order) => (
            <div key={order.id} className="bg-gray-800 rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-white font-semibold">#{order.id}</h3>
                  <p className="text-gray-400 text-sm">{order.date}</p>
                </div>
                <div className="text-right">
                  <Badge className={getStatusColor(order.status)}>{getStatusText(order.status)}</Badge>
                  <p className="text-orange-400 font-bold mt-1">${order.total}</p>
                </div>
              </div>

              <div className="space-y-2">
                {order.items.map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="text-gray-300">{item.name}</span>
                    <span className="text-gray-400">${item.price}</span>
                  </div>
                ))}
              </div>

              <div className="flex space-x-2 pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-gray-600 text-gray-300 hover:bg-gray-700 bg-transparent"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  {language === "en" ? "View" : "عرض"}
                </Button>
                {order.status === "completed" && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-orange-400 text-orange-400 hover:bg-orange-400 hover:text-white bg-transparent"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    {language === "en" ? "Download" : "تحميل"}
                  </Button>
                )}
              </div>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  )
}
