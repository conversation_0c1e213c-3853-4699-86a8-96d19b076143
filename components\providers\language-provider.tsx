"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

type Language = "en" | "ar"

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
  isHydrated: boolean
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

const translations = {
  en: {
    // Navigation & Basic
    login: "Login",
    signup: "Sign Up",
    home: "Home",
    accounts: "Accounts",
    tools: "Tools",
    profile: "Profile",
    search: "Search...",
    featured: "Featured Products",
    viewAll: "View All",
    buyNow: "Buy Now",
    price: "Price",
    level: "Level",
    rank: "Rank",
    skins: "Skins",
    inStock: "In Stock",
    outOfStock: "Out of Stock",
    reviews: "Reviews",
    features: "Features",

    // Order Management
    orderHistory: "Order History",
    ordersManagement: "Orders Management",
    orderDetails: "Order Details",
    orderNumber: "Order Number",
    orderStatus: "Order Status",
    paymentStatus: "Payment Status",
    paymentMethod: "Payment Method",
    totalOrders: "Total Orders",
    totalRevenue: "Total Revenue",
    averageOrderValue: "Avg Order Value",
    pendingOrders: "Pending Orders",

    // Order Status
    pending: "Pending",
    processing: "Processing",
    completed: "Completed",
    cancelled: "Cancelled",
    refunded: "Refunded",
    active: "Active",
    inactive: "Inactive",
    sold: "Sold",

    // Payment
    paid: "Paid",
    failed: "Failed",
    creditCard: "Credit Card",
    paypal: "PayPal",
    crypto: "Crypto",
    bankTransfer: "Bank Transfer",

    // Payment Methods (without underscore)
    creditcard: "Credit Card",

    // Actions
    view: "View",
    edit: "Edit",
    delete: "Delete",
    cancel: "Cancel",
    save: "Save",
    export: "Export",
    refresh: "Refresh",
    download: "Download",
    reorder: "Reorder",
    back: "Back",
    continue: "Continue",

    // Filters & Search
    filters: "Filters",
    allStatus: "All Status",
    allTypes: "All Types",
    allPlatforms: "All Platforms",
    sortBy: "Sort by",
    sortByDate: "Sort by Date",
    sortByAmount: "Sort by Amount",
    newestFirst: "Newest First",
    oldestFirst: "Oldest First",
    clearFilters: "Clear Filters",

    // Purchase Flow
    reviewPurchase: "Review Purchase",
    paymentDetails: "Payment Details",
    processingPayment: "Processing Payment",
    purchaseComplete: "Purchase Complete",
    orderSummary: "Order Summary",
    subtotal: "Subtotal",
    tax: "Tax",
    discount: "Discount",
    total: "Total",
    continueToPayment: "Continue to Payment",
    purchaseSuccessful: "Purchase Successful!",
    continueShopping: "Continue Shopping",

    // Product Types
    hack: "Hack",
    account: "Account",
    type: "Type",
    platform: "Platform",
    version: "Version",
    company: "Company",
    requirements: "Requirements",
    downloads: "Downloads",
    lastUpdated: "Last Updated",
    created: "Created",
    updated: "Updated",

    // Admin
    adminDashboard: "Admin Dashboard",
    accountsManagement: "Accounts Management",
    hacksManagement: "Hacks Management",
    userManagement: "User Management",

    // Messages
    noOrdersFound: "No orders found",
    noOrdersMessage: "You haven't placed any orders yet or no orders match your search criteria.",
    orderConfirmed: "has been confirmed",
    paymentSecure: "Your payment information is encrypted and secure",
    processingMessage: "Please wait while we process your payment",

    // Profile
    personalInformation: "Personal Information",
    accountSettings: "Account Settings",
    preferences: "Preferences",
    emailNotifications: "Email Notifications",
    notificationDescription: "Receive notifications about your orders",
    fullName: "Full Name",
    emailAddress: "Email Address",
    saveChanges: "Save Changes",
    premiumMember: "Premium Member",
    joined: "Joined",
    spent: "Spent",

    // Common
    name: "Name",
    description: "Description",
    status: "Status",
    date: "Date",
    amount: "Amount",
    customer: "Customer",
    items: "Items",
    actions: "Actions",
    loading: "Loading...",
    error: "Error",
    success: "Success",
    warning: "Warning",
    info: "Info",
  },
  ar: {
    // Navigation & Basic
    login: "تسجيل الدخول",
    signup: "إنشاء حساب",
    home: "الرئيسية",
    accounts: "الحسابات",
    tools: "الأدوات",
    profile: "الملف الشخصي",
    search: "البحث...",
    featured: "المنتجات المميزة",
    viewAll: "عرض الكل",
    buyNow: "اشتري الآن",
    price: "السعر",
    level: "المستوى",
    rank: "الرتبة",
    skins: "الأشكال",
    inStock: "متوفر",
    outOfStock: "غير متوفر",
    reviews: "التقييمات",
    features: "المميزات",

    // Order Management
    orderHistory: "تاريخ الطلبات",
    ordersManagement: "إدارة الطلبات",
    orderDetails: "تفاصيل الطلب",
    orderNumber: "رقم الطلب",
    orderStatus: "حالة الطلب",
    paymentStatus: "حالة الدفع",
    paymentMethod: "طريقة الدفع",
    totalOrders: "إجمالي الطلبات",
    totalRevenue: "إجمالي الإيرادات",
    averageOrderValue: "متوسط قيمة الطلب",
    pendingOrders: "الطلبات المعلقة",

    // Order Status
    pending: "قيد الانتظار",
    processing: "قيد المعالجة",
    completed: "مكتمل",
    cancelled: "ملغي",
    refunded: "مسترد",
    active: "نشط",
    inactive: "غير نشط",
    sold: "مباع",

    // Payment
    paid: "مدفوع",
    failed: "فشل",
    creditCard: "بطاقة ائتمان",
    paypal: "باي بال",
    crypto: "عملة رقمية",
    bankTransfer: "تحويل بنكي",

    // Payment Methods (without underscore)
    creditcard: "بطاقة ائتمان",

    // Actions
    view: "عرض",
    edit: "تعديل",
    delete: "حذف",
    cancel: "إلغاء",
    save: "حفظ",
    export: "تصدير",
    refresh: "تحديث",
    download: "تحميل",
    reorder: "إعادة طلب",
    back: "رجوع",
    continue: "متابعة",

    // Filters & Search
    filters: "المرشحات",
    allStatus: "جميع الحالات",
    allTypes: "جميع الأنواع",
    allPlatforms: "جميع المنصات",
    sortBy: "ترتيب حسب",
    sortByDate: "ترتيب حسب التاريخ",
    sortByAmount: "ترتيب حسب المبلغ",
    newestFirst: "الأحدث أولاً",
    oldestFirst: "الأقدم أولاً",
    clearFilters: "مسح المرشحات",

    // Purchase Flow
    reviewPurchase: "مراجعة الشراء",
    paymentDetails: "تفاصيل الدفع",
    processingPayment: "معالجة الدفع",
    purchaseComplete: "تم الشراء",
    orderSummary: "ملخص الطلب",
    subtotal: "المجموع الفرعي",
    tax: "الضريبة",
    discount: "الخصم",
    total: "المجموع",
    continueToPayment: "متابعة للدفع",
    purchaseSuccessful: "تم الشراء بنجاح!",
    continueShopping: "متابعة التسوق",

    // Product Types
    hack: "هاك",
    account: "حساب",
    type: "النوع",
    platform: "المنصة",
    version: "الإصدار",
    company: "الشركة",
    requirements: "المتطلبات",
    downloads: "التحميلات",
    lastUpdated: "آخر تحديث",
    created: "تاريخ الإنشاء",
    updated: "آخر تحديث",

    // Admin
    adminDashboard: "لوحة الإدارة",
    accountsManagement: "إدارة الحسابات",
    hacksManagement: "إدارة الهاكات",
    userManagement: "إدارة المستخدمين",

    // Messages
    noOrdersFound: "لم يتم العثور على طلبات",
    noOrdersMessage: "لم تقم بوضع أي طلبات بعد أو لا توجد طلبات تطابق معايير البحث الخاصة بك.",
    orderConfirmed: "تم تأكيد",
    paymentSecure: "معلومات الدفع الخاصة بك مشفرة وآمنة",
    processingMessage: "يرجى الانتظار بينما نعالج دفعتك",

    // Profile
    personalInformation: "المعلومات الشخصية",
    accountSettings: "إعدادات الحساب",
    preferences: "التفضيلات",
    emailNotifications: "إشعارات البريد الإلكتروني",
    notificationDescription: "تلقي إشعارات حول طلباتك",
    fullName: "الاسم الكامل",
    emailAddress: "عنوان البريد الإلكتروني",
    saveChanges: "حفظ التغييرات",
    premiumMember: "عضو مميز",
    joined: "انضم في",
    spent: "مُنفق",

    // Common
    name: "الاسم",
    description: "الوصف",
    status: "الحالة",
    date: "التاريخ",
    amount: "المبلغ",
    customer: "العميل",
    items: "العناصر",
    actions: "الإجراءات",
    loading: "جاري التحميل...",
    error: "خطأ",
    success: "نجح",
    warning: "تحذير",
    info: "معلومات",
  },
}

export function LanguageProvider({
  children,
  locale,
}: {
  children: ReactNode
  locale?: string
}) {
  // Always start with server-safe default to prevent hydration mismatch
  const [language, setLanguage] = useState<Language>((locale as Language) || "en")
  const [isHydrated, setIsHydrated] = useState(false)

  // Handle client-side hydration and localStorage
  useEffect(() => {
    setIsHydrated(true)

    // Only check localStorage after hydration to prevent mismatch
    const savedLanguage = localStorage.getItem("language")
    if (savedLanguage === "en" || savedLanguage === "ar") {
      setLanguage(savedLanguage)
    }
  }, [])

  // Save language to localStorage whenever it changes (only after hydration)
  useEffect(() => {
    if (isHydrated) {
      localStorage.setItem("language", language)
    }
  }, [language, isHydrated])

  const t = (key: string): string => {
    return translations[language][key as keyof (typeof translations)["en"]] || key
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, isHydrated }}>
      <div className={language === "ar" ? "font-cairo" : "font-inter"} dir={language === "ar" ? "rtl" : "ltr"}>
        {children}
      </div>
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}
