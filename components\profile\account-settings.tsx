"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { useLanguage } from "@/components/providers/language-provider"

interface User {
  id: string
  name: string
  email: string
  avatar: string
  joinDate: string
  totalOrders: number
  totalSpent: number
}

interface AccountSettingsProps {
  user: User
}

export function AccountSettings({ user }: AccountSettingsProps) {
  const { language, t } = useLanguage()
  const [formData, setFormData] = useState({
    name: user.name,
    email: user.email,
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // ## TODO: Update user settings in Supabase
    console.log("Update settings:", formData)
  }

  return (
    <div className="space-y-6">
      <Card className="bg-gray-900 border-zinc-700">
        <CardHeader>
          <CardTitle className="text-white">
            {t('personalInformation')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-zinc-300">
                {t('fullName')}
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="bg-gray-800 border-zinc-600 text-white"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-zinc-300">
                {t('emailAddress')}
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="bg-gray-800 border-zinc-600 text-white"
              />
            </div>

            <Button type="submit" className="bg-orange-500 hover:bg-orange-600 text-white">
              {t('saveChanges')}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card className="bg-gray-900 border-zinc-700">
        <CardHeader>
          <CardTitle className="text-red-500">
            {language === "en" ? "Danger Zone" : "منطقة الخطر"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-zinc-400 text-sm mb-4">
            {language === "en"
              ? "Once you delete your account, there is no going back. Please be certain."
              : "بمجرد حذف حسابك، لا يمكن التراجع. يرجى التأكد."}
          </p>
          <Button variant="destructive">
            {language === "en" ? "Delete Account" : "حذف الحساب"}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
