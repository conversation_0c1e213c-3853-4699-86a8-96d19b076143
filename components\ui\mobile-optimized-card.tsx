"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface MobileOptimizedCardProps {
  children: React.ReactNode
  className?: string
  onClick?: () => void
  isSquare?: boolean
}

export function MobileOptimizedCard({ 
  children, 
  className = "", 
  onClick,
  isSquare = true 
}: MobileOptimizedCardProps) {
  return (
    <Card
      className={cn(
        "bg-background border-border hover:border-orange-400 transition-all duration-300 group h-full flex flex-col cursor-pointer touch-target",
        "mobile-card-spacing sm:p-0", // Mobile-specific spacing
        className
      )}
      onClick={onClick}
    >
      {children}
    </Card>
  )
}

interface MobileOptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  isSquare?: boolean
}

export function MobileOptimizedImage({
  src,
  alt,
  width = 300,
  height = 300,
  className = "",
  isSquare = true
}: MobileOptimizedImageProps) {
  return (
    <div className="relative overflow-hidden rounded-t-lg">
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={cn(
          "w-full responsive-image group-hover:scale-105 transition-transform duration-300",
          isSquare ? "aspect-square" : "h-32 sm:h-40",
          className
        )}
        loading="lazy"
      />
    </div>
  )
}

interface MobileOptimizedBadgeProps {
  children: React.ReactNode
  position: "top-left" | "top-right"
  variant?: "default" | "secondary" | "destructive"
  className?: string
}

export function MobileOptimizedBadge({
  children,
  position,
  variant = "default",
  className = ""
}: MobileOptimizedBadgeProps) {
  const positionClasses = {
    "top-left": "absolute top-1 left-1 sm:top-2 sm:left-2",
    "top-right": "absolute top-1 right-1 sm:top-2 sm:right-2"
  }

  return (
    <Badge
      className={cn(
        positionClasses[position],
        "text-xs px-1 py-0.5 sm:px-2 sm:py-1",
        className
      )}
      variant={variant}
    >
      {children}
    </Badge>
  )
}

interface MobileOptimizedContentProps {
  children: React.ReactNode
  className?: string
}

export function MobileOptimizedContent({
  children,
  className = ""
}: MobileOptimizedContentProps) {
  return (
    <CardContent className={cn(
      "p-2 sm:p-3 md:p-4 flex-1 card-content-height",
      className
    )}>
      <div className="space-y-2 sm:space-y-3 h-full flex flex-col">
        {children}
      </div>
    </CardContent>
  )
}

interface MobileOptimizedTitleProps {
  children: React.ReactNode
  className?: string
}

export function MobileOptimizedTitle({
  children,
  className = ""
}: MobileOptimizedTitleProps) {
  return (
    <h3 className={cn(
      "font-bold text-xs sm:text-sm md:text-base line-clamp-2 mb-1 leading-tight",
      className
    )}>
      {children}
    </h3>
  )
}

interface MobileOptimizedDescriptionProps {
  children: React.ReactNode
  className?: string
  lines?: 2 | 3
}

export function MobileOptimizedDescription({
  children,
  className = "",
  lines = 2
}: MobileOptimizedDescriptionProps) {
  return (
    <p className={cn(
      "text-muted-foreground text-xs sm:text-sm leading-tight",
      lines === 2 ? "line-clamp-2" : "line-clamp-3",
      className
    )}>
      {children}
    </p>
  )
}

interface MobileOptimizedFooterProps {
  children: React.ReactNode
  className?: string
}

export function MobileOptimizedFooter({
  children,
  className = ""
}: MobileOptimizedFooterProps) {
  return (
    <CardFooter className={cn(
      "p-2 sm:p-3 md:p-4 pt-0",
      className
    )}>
      <div className="w-full">
        {children}
      </div>
    </CardFooter>
  )
}

interface MobileOptimizedButtonProps {
  children: React.ReactNode
  onClick?: (e: React.MouseEvent) => void
  disabled?: boolean
  className?: string
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
}

export function MobileOptimizedButton({
  children,
  onClick,
  disabled = false,
  className = "",
  variant = "default",
  size = "sm"
}: MobileOptimizedButtonProps) {
  return (
    <Button
      size={size}
      variant={variant}
      className={cn(
        "w-full bg-orange-500 hover:bg-orange-600 text-white text-xs sm:text-sm touch-target min-h-[36px] sm:min-h-[40px]",
        className
      )}
      disabled={disabled}
      onClick={onClick}
    >
      {children}
    </Button>
  )
}

interface MobileOptimizedPriceProps {
  price: number | string
  className?: string
}

export function MobileOptimizedPrice({
  price,
  className = ""
}: MobileOptimizedPriceProps) {
  return (
    <div className={cn(
      "text-sm sm:text-lg md:text-xl font-bold text-orange-400",
      className
    )}>
      ${price}
    </div>
  )
}

// Export all components as a group for easier importing
export const MobileCard = {
  Root: MobileOptimizedCard,
  Header: CardHeader,
  Image: MobileOptimizedImage,
  Badge: MobileOptimizedBadge,
  Content: MobileOptimizedContent,
  Title: MobileOptimizedTitle,
  Description: MobileOptimizedDescription,
  Footer: MobileOptimizedFooter,
  Button: MobileOptimizedButton,
  Price: MobileOptimizedPrice
}
