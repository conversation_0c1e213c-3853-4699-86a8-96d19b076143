"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, <PERSON>Footer, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, ArrowRight, CreditCard } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"
import { FeaturedProductsSkeleton } from "@/components/ui/skeleton-card"
import { ProductViewModal } from "@/components/ui/product-view-modal"
import { PurchaseFlow } from "@/components/purchase/purchase-flow"
import { Account } from "@/types/account"
import { Hack } from "@/types/hack"

interface FeaturedProductsProps {
  products: {
    accounts: Account[]
    hacks: Hack[]
  }
  isLoading?: boolean
}

export function FeaturedProducts({ products, isLoading = false }: FeaturedProductsProps) {
  const { language, t } = useLanguage()

  // Modal states
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<Account | Hack | null>(null)
  const [selectedProductType, setSelectedProductType] = useState<'account' | 'hack'>('account')
  const [isPurchaseOpen, setIsPurchaseOpen] = useState(false)
  const [purchaseProduct, setPurchaseProduct] = useState<Account | Hack | null>(null)

  // Show loading skeleton if loading
  if (isLoading) {
    return <FeaturedProductsSkeleton />
  }

  // Modal handlers
  const handleViewProduct = (product: Account | Hack, type: 'account' | 'hack') => {
    setSelectedProduct(product)
    setSelectedProductType(type)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedProduct(null)
  }

  const handleBuyNow = (product: Account | Hack) => {
    setPurchaseProduct(product)
    setIsPurchaseOpen(true)
  }

  const handleClosePurchase = () => {
    setIsPurchaseOpen(false)
    setPurchaseProduct(null)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  }

  return (
    <section className="py-20 bg-zinc-900">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            {language === "en" ? "Featured Products" : "المنتجات المميزة"}
          </h2>
          <p className="text-zinc-400 text-lg max-w-2xl mx-auto">
            {language === "en"
              ? "Discover our most popular PUBG accounts and gaming hacks"
              : "اكتشف أشهر حسابات PUBG وهاكات الألعاب لدينا"}
          </p>
        </motion.div>

        {/* Featured Accounts */}
        <div className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <h3 className="text-2xl font-bold text-white">
              {language === "en" ? "Premium Accounts" : "الحسابات المميزة"}
            </h3>
            <Link href="/accounts">
              <Button
                variant="outline"
                className="border-orange-400 text-orange-400 hover:bg-orange-400 hover:text-white bg-transparent"
              >
                {language === "en" ? "View All" : "عرض الكل"}
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </Link>
          </div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-4 md:gap-6"
          >
            {products.accounts.map((account) => (
              <motion.div key={account.id} variants={itemVariants}>
                <Card
                  className="bg-black border-zinc-700 hover:border-orange-400 transition-colors group h-full flex flex-col cursor-pointer touch-target"
                  onClick={() => handleViewProduct(account, 'account')}
                >
                  <CardHeader className="p-0">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <Image
                        src={account.imageUrl || "/mido-logo.jpg"}
                        alt={account.name[language]}
                        width={400}
                        height={400}
                        className="w-full aspect-square object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      {account.isFeatured && (
                        <Badge className="absolute top-1 left-1 sm:top-2 sm:left-2 md:top-4 md:left-4 bg-orange-500 text-white text-xs px-1 py-0.5 sm:px-2 sm:py-1">
                          <span className="hidden sm:inline">{language === "en" ? "Featured" : "مميز"}</span>
                          <span className="sm:hidden">★</span>
                        </Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="p-2 sm:p-4 md:p-6 flex-1 flex flex-col card-content-height">
                    <h4 className="text-xs sm:text-sm md:text-xl font-semibold text-white mb-1 sm:mb-2 line-clamp-2 min-h-[2rem] sm:min-h-[2.5rem] md:min-h-[3rem] leading-tight">
                      {account.name[language]}
                    </h4>
                    <p className="text-zinc-400 text-xs sm:text-sm line-clamp-2 mb-2 sm:mb-4 leading-tight">
                      {account.description[language]}
                    </p>
                    <div className="flex items-center space-x-1 sm:space-x-2 mb-2 sm:mb-4 mt-auto">
                      <span className="text-zinc-400 text-xs sm:text-sm text-truncate-mobile">
                        {language === "en" ? "Premium Account" : "حساب مميز"}
                      </span>
                    </div>
                  </CardContent>
                  <CardFooter className="p-2 sm:p-4 md:p-6 pt-0 mt-auto">
                    <div className="space-y-2 w-full">
                      <div className="flex items-center justify-center">
                        <span className="text-sm sm:text-lg md:text-2xl font-bold text-orange-400">${account.price}</span>
                      </div>
                      <Button
                        className="w-full bg-orange-500 hover:bg-orange-600 text-white text-xs sm:text-sm touch-target min-h-[36px] sm:min-h-[40px]"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleBuyNow(account)
                        }}
                      >
                        <CreditCard className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                        <span className="truncate">{language === "en" ? "Buy Now" : "اشتري الآن"}</span>
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Featured Hacks */}
        <div>
          <div className="flex items-center justify-between mb-8">
            <h3 className="text-2xl font-bold text-white">{language === "en" ? "Gaming Hacks" : "هاكات الألعاب"}</h3>
            <Link href="/hacks">
              <Button
                variant="outline"
                className="border-orange-400 text-orange-400 hover:bg-orange-400 hover:text-white bg-transparent"
              >
                {language === "en" ? "View All" : "عرض الكل"}
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </Link>
          </div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-4 md:gap-6"
          >
            {products.hacks.map((hack) => (
              <motion.div key={hack.id} variants={itemVariants}>
                <Card
                  className="bg-black border-zinc-700 hover:border-orange-400 transition-colors group h-full flex flex-col cursor-pointer"
                  onClick={() => handleViewProduct(hack, 'hack')}
                >
                  <CardHeader className="p-0">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <Image
                        src={hack.imageUrl || "/mido-logo.jpg"}
                        alt={hack.name[language]}
                        width={400}
                        height={400}
                        className="w-full aspect-square object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      {hack.isFeatured && (
                        <Badge className="absolute top-2 left-2 sm:top-4 sm:left-4 bg-green-500 text-white text-xs sm:text-sm">
                          {language === "en" ? "Safe" : "آمن"}
                        </Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="p-3 sm:p-6 flex-1 flex flex-col">
                    <h4 className="text-sm sm:text-xl font-semibold text-white mb-1 sm:mb-2 line-clamp-2 min-h-[2.5rem] sm:min-h-[3rem]">
                      {hack.name[language]}
                    </h4>
                    <p className="text-zinc-400 text-xs sm:text-sm line-clamp-2 mb-2 sm:mb-4">
                      {hack.description[language]}
                    </p>
                    <div className="flex items-center space-x-1 sm:space-x-2 mb-2 sm:mb-4">
                      <span className="text-zinc-400 text-xs sm:text-sm truncate">
                        {language === "en" ? "Gaming Tool" : "أداة ألعاب"}
                      </span>
                      <span className="text-zinc-500 text-xs sm:text-sm">•</span>
                      <span className="text-zinc-400 text-xs sm:text-sm">
                        v{hack.version}
                      </span>
                    </div>
                  </CardContent>
                  <CardFooter className="p-3 sm:p-6 pt-0 mt-auto">
                    <div className="flex items-center justify-between w-full">
                      <span className="text-lg sm:text-2xl font-bold text-orange-400">${hack.price}</span>
                      <Button
                        className="bg-orange-500 hover:bg-orange-600 text-white text-xs sm:text-sm px-2 sm:px-4 py-1 sm:py-2"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleBuyNow(hack)
                        }}
                      >
                        <CreditCard className="w-3 h-3 mr-1" />
                        {language === "en" ? "Buy Now" : "اشتري الآن"}
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>

      {/* Product View Modal */}
      {selectedProduct && (
        <ProductViewModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          product={selectedProduct}
          productType={selectedProductType}
          onBuyNow={() => {
            handleCloseModal()
            handleBuyNow(selectedProduct)
          }}
        />
      )}

      {/* Purchase Flow Modal */}
      {purchaseProduct && (
        <PurchaseFlow
          isOpen={isPurchaseOpen}
          onClose={handleClosePurchase}
          product={purchaseProduct}
        />
      )}
    </section>
  )
}
