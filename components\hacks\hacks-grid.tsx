"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ooter, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, CreditCard, Download, Eye, Smartphone, Monitor } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"
import { ProductViewModal } from "@/components/ui/product-view-modal"
import { PurchaseFlow } from "@/components/purchase/purchase-flow"

import { Hack, getHackSpecDisplayText } from "@/types/hack"

interface HacksGridProps {
  hacks: Hack[]
}

export function HacksGrid({ hacks }: HacksGridProps) {
  const { language } = useLanguage()
  const [selectedHack, setSelectedHack] = useState<Hack | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isPurchaseOpen, setIsPurchaseOpen] = useState(false)
  const [purchaseHack, setPurchaseHack] = useState<Hack | null>(null)

  const handleViewHack = (hack: Hack) => {
    setSelectedHack(hack)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedHack(null)
  }

  const handleBuyNow = (hack: Hack) => {
    setPurchaseHack(hack)
    setIsPurchaseOpen(true)
  }

  const handleClosePurchase = () => {
    setIsPurchaseOpen(false)
    setPurchaseHack(null)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  }

  return (
    <>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-2 sm:gap-4 md:gap-6"
      >
        {hacks.map((hack) => (
          <motion.div key={hack.id} variants={itemVariants}>
            <Card
              className="bg-background border-border hover:border-orange-400 transition-all duration-300 group h-full flex flex-col cursor-pointer touch-target"
              onClick={() => handleViewHack(hack)}
            >
              <CardHeader className="p-0">
                <div className="relative overflow-hidden rounded-t-lg">
                  <Image
                    src={hack.imageUrl || "/mido-logo.jpg"}
                    alt={hack.name[language]}
                    width={300}
                    height={300}
                    className="w-full aspect-square object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  {hack.isFeatured && (
                    <Badge className="absolute top-1 left-1 sm:top-2 sm:left-2 bg-orange-500 text-white text-xs px-1 py-0.5">
                      <Star className="w-2.5 h-2.5 sm:w-3 sm:h-3 mr-0.5 sm:mr-1" />
                      <span className="hidden sm:inline">{language === "en" ? "Featured" : "مميز"}</span>
                      <span className="sm:hidden">{language === "en" ? "★" : "★"}</span>
                    </Badge>
                  )}
                  <Badge
                    className="absolute top-1 right-1 sm:top-2 sm:right-2 text-xs px-1 py-0.5"
                    variant={hack.status === "active" ? "default" : "secondary"}
                  >
                    <span className="hidden sm:inline">{hack.status}</span>
                    <span className="sm:hidden">
                      {hack.status === "active" ? "●" : "○"}
                    </span>
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="p-2 sm:p-3 md:p-4 flex-1 card-content-height">
                <div className="space-y-2 sm:space-y-3 h-full flex flex-col">
                  <div className="flex-1">
                    <h3 className="font-bold text-xs sm:text-sm md:text-base line-clamp-2 mb-1 leading-tight">
                      {hack.name[language]}
                    </h3>
                    <p className="text-muted-foreground text-xs sm:text-sm line-clamp-2 leading-tight">
                      {hack.description[language]}
                    </p>
                  </div>

                  <div className="flex items-center justify-between text-xs mt-auto">
                    <div className="flex items-center space-x-1 min-w-0 flex-1">
                      {hack.type === 'pc' ? (
                        <Monitor className="w-2.5 h-2.5 sm:w-3 sm:h-3 flex-shrink-0" />
                      ) : (
                        <Smartphone className="w-2.5 h-2.5 sm:w-3 sm:h-3 flex-shrink-0" />
                      )}
                      <span className="capitalize text-truncate-mobile">{hack.type}</span>
                      {hack.platform && (
                        <>
                          <span className="hidden sm:inline">•</span>
                          <span className="capitalize text-truncate-mobile hidden sm:inline">{hack.platform}</span>
                        </>
                      )}
                    </div>
                    <div className="text-muted-foreground text-xs flex-shrink-0 ml-1">
                      v{hack.version}
                    </div>
                  </div>

                  <div className="flex items-center justify-end mt-1">
                    <div className="text-sm sm:text-lg md:text-xl font-bold text-orange-400">
                      ${hack.price}
                    </div>
                  </div>
                </div>
              </CardContent>

              <CardFooter className="p-2 sm:p-3 md:p-4 pt-0">
                <div className="w-full">
                  <Button
                    size="sm"
                    className="w-full bg-orange-500 hover:bg-orange-600 text-white text-xs sm:text-sm touch-target min-h-[36px] sm:min-h-[40px]"
                    disabled={hack.status !== "active"}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleBuyNow(hack)
                    }}
                  >
                    <CreditCard className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                    <span className="truncate">{language === "en" ? "Buy Now" : "اشتري الآن"}</span>
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      <ProductViewModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        product={selectedHack}
        productType="hack"
      />

      <PurchaseFlow
        isOpen={isPurchaseOpen}
        onClose={handleClosePurchase}
        product={purchaseHack}
        productType="hack"
      />
    </>
  )
}
