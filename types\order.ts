/**
 * Order Management Types
 * 
 * ## Database Schema Notes:
 * - Orders table should reference both hacks and accounts as purchasable products
 * - Use polymorphic relationship with product_type and product_id fields
 * - All monetary values should be stored as decimal(10,2) in database
 * - Order status should be managed with proper state transitions
 * - Payment information should be stored securely with encryption
 * - Created/updated timestamps should be automatically managed
 * - User relationship should be established via foreign key to users table
 */

import { BilingualText } from './account';

// Enum types for order management
export type OrderStatus = 'pending' | 'processing' | 'completed' | 'cancelled' | 'refunded';
export type ProductType = 'hack' | 'account';
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';
export type PaymentMethod = 'credit_card' | 'paypal' | 'crypto' | 'bank_transfer';

// Order item interface for individual products in an order
export interface OrderItem {
  id: string;
  
  // ## Database field: order_id (UUID) - Foreign key to orders table
  orderId: string;
  
  // ## Database field: product_type (ENUM) - hack or account
  productType: ProductType;
  
  // ## Database field: product_id (UUID) - Reference to hack or account
  productId: string;
  
  // ## Database field: product_name (JSON) - Bilingual product name (snapshot)
  productName: BilingualText;
  
  // ## Database field: product_description (JSON) - Bilingual description (snapshot)
  productDescription: BilingualText;
  
  // ## Database field: unit_price (DECIMAL(10,2)) - Price per unit at time of purchase
  unitPrice: number;
  
  // ## Database field: quantity (INT) - Number of items (usually 1 for digital products)
  quantity: number;
  
  // ## Database field: total_price (DECIMAL(10,2)) - unit_price * quantity
  totalPrice: number;
  
  // ## Database field: product_image_url (VARCHAR) - Product image snapshot
  productImageUrl?: string;
  
  // ## Database field: created_at (TIMESTAMP)
  createdAt: string;
}

// Main order interface
export interface Order {
  id: string;
  
  // ## Database field: order_number (VARCHAR) - Human-readable order number
  orderNumber: string;
  
  // ## Database field: user_id (UUID) - Foreign key to users table
  userId: string;
  
  // ## Database field: user_email (VARCHAR) - User email snapshot
  userEmail: string;
  
  // ## Database field: user_name (VARCHAR) - User name snapshot
  userName: string;
  
  // ## Database field: status (ENUM) - Order status
  status: OrderStatus;
  
  // ## Database field: payment_status (ENUM) - Payment status
  paymentStatus: PaymentStatus;
  
  // ## Database field: payment_method (ENUM) - Payment method used
  paymentMethod: PaymentMethod;
  
  // ## Database field: subtotal (DECIMAL(10,2)) - Sum of all item prices
  subtotal: number;
  
  // ## Database field: tax_amount (DECIMAL(10,2)) - Tax amount if applicable
  taxAmount: number;
  
  // ## Database field: discount_amount (DECIMAL(10,2)) - Discount applied
  discountAmount: number;
  
  // ## Database field: total_amount (DECIMAL(10,2)) - Final total amount
  totalAmount: number;
  
  // ## Database field: currency (VARCHAR) - Currency code (USD, EUR, etc.)
  currency: string;
  
  // ## Database field: payment_transaction_id (VARCHAR) - External payment reference
  paymentTransactionId?: string;
  
  // ## Database field: notes (TEXT) - Order notes or special instructions
  notes?: string;
  
  // ## Database field: created_at (TIMESTAMP)
  createdAt: string;
  
  // ## Database field: updated_at (TIMESTAMP)
  updatedAt: string;
  
  // ## Database field: completed_at (TIMESTAMP) - When order was completed
  completedAt?: string;
  
  // ## Database field: cancelled_at (TIMESTAMP) - When order was cancelled
  cancelledAt?: string;
  
  // ## Database field: refunded_at (TIMESTAMP) - When order was refunded
  refundedAt?: string;
  
  // Order items (from order_items table)
  items: OrderItem[];
}

// Interface for creating new orders
export interface CreateOrderRequest {
  userId: string;
  items: {
    productType: ProductType;
    productId: string;
    quantity: number;
  }[];
  paymentMethod: PaymentMethod;
  notes?: string;
}

// Interface for updating order status
export interface UpdateOrderStatusRequest {
  orderId: string;
  status: OrderStatus;
  paymentStatus?: PaymentStatus;
  paymentTransactionId?: string;
  notes?: string;
}

// Order filters for search and filtering
export interface OrderFilters {
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  paymentMethod?: PaymentMethod;
  userId?: string;
  userEmail?: string;
  orderNumber?: string;
  productType?: ProductType;
  dateFrom?: string;
  dateTo?: string;
  amountMin?: number;
  amountMax?: number;
  search?: string; // Search in order number, user email, user name
}

// Order list response for pagination
export interface OrderListResponse {
  orders: Order[];
  total: number;
  page: number;
  limit: number;
}

// Order statistics for admin dashboard
export interface OrderStats {
  total: number;
  pending: number;
  processing: number;
  completed: number;
  cancelled: number;
  refunded: number;
  totalRevenue: number;
  averageOrderValue: number;
  totalOrders: number;
  recentOrders: Order[];
  topProducts: {
    productId: string;
    productName: BilingualText;
    productType: ProductType;
    totalSales: number;
    revenue: number;
  }[];
}

// User order history interface
export interface UserOrderHistory {
  orders: Order[];
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lastOrderDate?: string;
}

// Payment information interface
export interface PaymentInfo {
  method: PaymentMethod;
  transactionId?: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  processedAt?: string;
  failureReason?: string;
}

// Order activity log for tracking changes
export interface OrderActivity {
  id: string;
  
  // ## Database field: order_id (UUID) - Foreign key to orders table
  orderId: string;
  
  // ## Database field: user_id (UUID) - Who performed the action
  userId: string;
  
  // ## Database field: action (VARCHAR) - Action performed
  action: string;
  
  // ## Database field: old_status (VARCHAR) - Previous status
  oldStatus?: string;
  
  // ## Database field: new_status (VARCHAR) - New status
  newStatus?: string;
  
  // ## Database field: notes (TEXT) - Additional notes
  notes?: string;
  
  // ## Database field: created_at (TIMESTAMP)
  createdAt: string;
}

// Helper functions
export const generateOrderNumber = (): string => {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `ORD-${timestamp.slice(-6)}-${random}`;
};

export const calculateOrderTotal = (items: OrderItem[], taxRate: number = 0, discountAmount: number = 0): {
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
} => {
  const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);
  const taxAmount = subtotal * taxRate;
  const totalAmount = subtotal + taxAmount - discountAmount;
  
  return {
    subtotal,
    taxAmount,
    discountAmount,
    totalAmount: Math.max(0, totalAmount)
  };
};

export const getOrderStatusColor = (status: OrderStatus): string => {
  switch (status) {
    case 'pending': return 'bg-yellow-500';
    case 'processing': return 'bg-blue-500';
    case 'completed': return 'bg-green-500';
    case 'cancelled': return 'bg-gray-500';
    case 'refunded': return 'bg-red-500';
    default: return 'bg-gray-500';
  }
};

export const getPaymentStatusColor = (status: PaymentStatus): string => {
  switch (status) {
    case 'pending': return 'bg-yellow-500';
    case 'paid': return 'bg-green-500';
    case 'failed': return 'bg-red-500';
    case 'refunded': return 'bg-orange-500';
    default: return 'bg-gray-500';
  }
};

// Default values
export const createEmptyOrder = (): Partial<Order> => ({
  status: 'pending',
  paymentStatus: 'pending',
  currency: 'USD',
  subtotal: 0,
  taxAmount: 0,
  discountAmount: 0,
  totalAmount: 0,
  items: []
});
