"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ban, UserCheck, Eye, Mail } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

export function UserManagement() {
  // ## TODO: Fetch users from Supabase: 'users' table with orders count
  const [users] = useState([
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      status: "active",
      totalOrders: 12,
      totalSpent: 1299,
      joinDate: "2023-06-15",
      lastActive: "2024-01-15",
    },
    {
      id: "2",
      name: "<PERSON>",
      email: "<EMAIL>",
      status: "active",
      totalOrders: 8,
      totalSpent: 899,
      joinDate: "2023-08-20",
      lastActive: "2024-01-14",
    },
    {
      id: "3",
      name: "<PERSON>",
      email: "<EMAIL>",
      status: "banned",
      totalOrders: 3,
      totalSpent: 299,
      joinDate: "2023-12-01",
      lastActive: "2024-01-10",
    },
  ])

  // ## TODO: Implement user management operations with Supabase
  const handleBanUser = (id: string) => {
    console.log("Ban user:", id)
  }

  const handleUnbanUser = (id: string) => {
    console.log("Unban user:", id)
  }

  const handleViewUser = (id: string) => {
    console.log("View user details:", id)
  }

  const handleContactUser = (id: string) => {
    console.log("Contact user:", id)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>User Management</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Orders</TableHead>
              <TableHead>Total Spent</TableHead>
              <TableHead>Join Date</TableHead>
              <TableHead>Last Active</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">{user.name}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>
                  <Badge variant={user.status === "active" ? "default" : "destructive"}>{user.status}</Badge>
                </TableCell>
                <TableCell>{user.totalOrders}</TableCell>
                <TableCell>${user.totalSpent}</TableCell>
                <TableCell>{user.joinDate}</TableCell>
                <TableCell>{user.lastActive}</TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" onClick={() => handleViewUser(user.id)}>
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleContactUser(user.id)}>
                      <Mail className="w-4 h-4" />
                    </Button>
                    {user.status === "active" ? (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleBanUser(user.id)}
                        className="text-red-500 hover:text-red-500/80"
                      >
                        <Ban className="w-4 h-4" />
                      </Button>
                    ) : (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleUnbanUser(user.id)}
                        className="text-green-500 hover:text-green-500/80"
                      >
                        <UserCheck className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
