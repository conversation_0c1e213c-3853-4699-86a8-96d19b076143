/**
 * Hack Management Types
 * 
 * ## Database Schema Notes:
 * - All bilingual fields should be stored as JSON objects with 'en' and 'ar' keys
 * - Price should be stored as decimal(10,2) in database
 * - Platform-specific fields should be nullable based on hack_type
 * - Company field allows multiple versions of same hack from different companies
 * - Created/updated timestamps should be automatically managed
 */

import { BilingualText } from './account';

// Enum types for hack specifications
export type HackType = 'mobile' | 'pc';
export type Platform = 'android' | 'ios';
export type RootStatus = 'root' | 'non-root';
export type JailbreakStatus = 'jailbroken' | 'non-jailbroken';

// Main hack interface with conditional fields
export interface Hack {
  id: string;
  
  // ## Database field: hack_name (JSON) - Bilingual hack name
  name: BilingualText;
  
  // ## Database field: hack_type (ENUM) - mobile or pc
  type: HackType;
  
  // ## Database field: platform (ENUM) - android, ios (nullable for PC hacks)
  platform?: Platform;
  
  // ## Database field: root_status (ENUM) - root, non-root (nullable, only for Android)
  rootStatus?: RootStatus;
  
  // ## Database field: jailbreak_status (ENUM) - jailbroken, non-jailbroken (nullable, only for iOS)
  jailbreakStatus?: JailbreakStatus;
  
  // ## Database field: price (DECIMAL(10,2)) - Hack price in USD
  price: number;
  
  // ## Database field: company (VARCHAR) - Company/developer name for multi-company support
  company: string;
  
  // ## Database field: version (VARCHAR) - Hack version
  version: string;
  
  // ## Database field: description (JSON) - Bilingual description
  description: BilingualText;
  
  // ## Database field: status (ENUM) - active, inactive, maintenance
  status: 'active' | 'inactive' | 'maintenance';
  
  // ## Database field: created_at (TIMESTAMP)
  createdAt: string;
  
  // ## Database field: updated_at (TIMESTAMP)
  updatedAt: string;
  
  // ## Database field: image_url (VARCHAR) - Optional hack image
  imageUrl?: string;
  
  // ## Database field: is_featured (BOOLEAN) - For homepage display
  isFeatured: boolean;
  
  // ## Database field: download_count (INT) - Track popularity
  downloadCount: number;
  
  // ## Database field: last_updated_date (DATE) - When hack was last updated
  lastUpdated: string;
}

// Form data interface for creating/editing hacks
export interface HackFormData {
  name: BilingualText;
  type: HackType;
  platform?: Platform;
  rootStatus?: RootStatus;
  jailbreakStatus?: JailbreakStatus;
  price: number;
  company: string;
  version: string;
  description: BilingualText;
  imageUrl?: string;
  isFeatured: boolean;
}

// Interface for hack creation/update API requests
export interface CreateHackRequest {
  name: BilingualText;
  type: HackType;
  platform?: Platform;
  rootStatus?: RootStatus;
  jailbreakStatus?: JailbreakStatus;
  price: number;
  company: string;
  version: string;
  description: BilingualText;
  imageUrl?: string;
  isFeatured?: boolean;
}

export interface UpdateHackRequest extends Partial<CreateHackRequest> {
  id: string;
}

// Hack list response for admin management
export interface HackListResponse {
  hacks: Hack[];
  total: number;
  page: number;
  limit: number;
}

// Hack filters for admin search/filter functionality
export interface HackFilters {
  type?: HackType;
  platform?: Platform;
  rootStatus?: RootStatus;
  jailbreakStatus?: JailbreakStatus;
  status?: Hack['status'];
  company?: string;
  priceMin?: number;
  priceMax?: number;
  search?: string; // Search in name and description
  isFeatured?: boolean;
}

// Hack statistics for admin dashboard
export interface HackStats {
  total: number;
  mobile: number;
  pc: number;
  android: number;
  ios: number;
  totalDownloads: number;
  totalRevenue: number;
  averagePrice: number;
  companiesCount: number;
}

// Helper functions
export const createEmptyBilingualText = (): BilingualText => ({
  en: '',
  ar: ''
});

export const createDefaultHackFormData = (): HackFormData => ({
  name: createEmptyBilingualText(),
  type: 'mobile',
  price: 0,
  company: '',
  version: '1.0',
  description: createEmptyBilingualText(),
  imageUrl: '',
  isFeatured: false
});

// Validation helpers
export const validateBilingualText = (text: BilingualText): boolean => {
  return text.en.trim().length > 0 && text.ar.trim().length > 0;
};

export const validateHackFormData = (data: HackFormData): string[] => {
  const errors: string[] = [];
  
  if (!validateBilingualText(data.name)) {
    errors.push('Hack name is required in both English and Arabic');
  }
  
  if (!validateBilingualText(data.description)) {
    errors.push('Description is required in both English and Arabic');
  }
  
  if (data.price <= 0) {
    errors.push('Price must be greater than 0');
  }
  
  if (!data.company.trim()) {
    errors.push('Company name is required');
  }
  
  if (!data.version.trim()) {
    errors.push('Version is required');
  }
  
  // Conditional validation based on hack type
  if (data.type === 'mobile') {
    if (!data.platform) {
      errors.push('Platform is required for mobile hacks');
    } else {
      if (data.platform === 'android' && !data.rootStatus) {
        errors.push('Root status is required for Android hacks');
      }
      if (data.platform === 'ios' && !data.jailbreakStatus) {
        errors.push('Jailbreak status is required for iOS hacks');
      }
    }
  }
  
  return errors;
};

// Helper to get display text for hack specifications
export const getHackSpecDisplayText = (hack: Hack, language: 'en' | 'ar'): string => {
  const parts: string[] = [];
  
  if (hack.type === 'mobile') {
    if (hack.platform === 'android') {
      parts.push(`Android (${hack.rootStatus === 'root' ? 'Root' : 'Non-root'})`);
    } else if (hack.platform === 'ios') {
      parts.push(`iOS (${hack.jailbreakStatus === 'jailbroken' ? 'Jailbroken' : 'Non-jailbroken'})`);
    }
  } else {
    parts.push('PC');
  }
  
  parts.push(`v${hack.version}`);
  parts.push(hack.company);
  
  return parts.join(' • ');
};
