"use client"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { AccountsManagement } from "./accounts-management"
import { HacksManagement } from "./hacks-management"
import { UserManagement } from "./user-management"
import { OrdersManagement } from "./orders-management"
import { useLanguage } from "@/components/providers/language-provider"

export function AdminDashboard() {
  const { t } = useLanguage()

  return (
    <div className="space-y-8">
      <Tabs defaultValue="orders" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="orders">{t('ordersManagement')}</TabsTrigger>
          <TabsTrigger value="accounts">{t('accountsManagement')}</TabsTrigger>
          <TabsTrigger value="hacks">{t('hacksManagement')}</TabsTrigger>
          <TabsTrigger value="users">{t('userManagement')}</TabsTrigger>
        </Ta<PERSON>List>

        <TabsContent value="orders" className="mt-6">
          <OrdersManagement />
        </TabsContent>

        <TabsContent value="accounts" className="mt-6">
          <AccountsManagement />
        </TabsContent>

        <TabsContent value="hacks" className="mt-6">
          <HacksManagement />
        </TabsContent>

        <TabsContent value="users" className="mt-6">
          <UserManagement />
        </TabsContent>
      </Tabs>
    </div>
  )
}
