"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, SmilePlus } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"

const items = [
  {
    icon: ShieldChe<PERSON>,
    key: "secure",
    en: { title: "Secure Transactions", desc: "All purchases are protected with top-grade security." },
    ar: { title: "معاملات آمنة", desc: "جميع المشتريات محمية بأعلى درجات الأمان." },
  },
  {
    icon: Zap,
    key: "fast",
    en: { title: "Instant Delivery", desc: "Get your account or tool right after payment." },
    ar: { title: "تسليم فوري", desc: "احصل على حسابك أو أداتك مباشرة بعد الدفع." },
  },
  {
    icon: SmilePlus,
    key: "support",
    en: { title: "24/7 Support", desc: "We’re here for you any time, day or night." },
    ar: { title: "دعم 24/7", desc: "نحن هنا من أجلك في أي وقت على مدار اليوم." },
  },
]

export function WhyChooseUs() {
  const { language } = useLanguage()

  return (
    <section className="py-20 bg-black">
      <div className="container mx-auto px-4">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-3xl md:text-4xl font-bold text-white text-center mb-12"
        >
          {language === "en" ? "Why Choose Us" : "لماذا تختارنا"}
        </motion.h2>

        <div className="grid grid-cols-3 gap-3 sm:gap-8">
          {items.map(({ icon: Icon, en, ar, key }) => (
            <motion.div
              key={key}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="bg-zinc-900 p-4 sm:p-8 rounded-lg text-center"
            >
              <Icon className="w-8 h-8 sm:w-12 sm:h-12 text-orange-400 mx-auto mb-2 sm:mb-4" />
              <h3 className="text-sm sm:text-xl font-semibold text-white mb-1 sm:mb-2">{language === "en" ? en.title : ar.title}</h3>
              <p className="text-zinc-400 text-xs sm:text-sm">{language === "en" ? en.desc : ar.desc}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
