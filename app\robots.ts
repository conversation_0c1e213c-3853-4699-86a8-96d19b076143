import type { MetadataRoute } from "next"

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: "*",
        allow: "/",
        disallow: [
          "/admin/",
          "/profile/",
          "/auth/",
          "/api/",
          "/_next/",
          "/private/",
          "*.json",
          "/search?*",
        ],
        crawlDelay: 1,
      },
      {
        userAgent: "Googlebot",
        allow: "/",
        disallow: ["/admin/", "/profile/", "/auth/", "/api/"],
      },
      {
        userAgent: "Bingbot",
        allow: "/",
        disallow: ["/admin/", "/profile/", "/auth/", "/api/"],
      },
    ],
    sitemap: "https://rngstore.vip/sitemap.xml",
    host: "https://rngstore.vip",
  }
}
