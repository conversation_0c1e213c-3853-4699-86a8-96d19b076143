"use client"

import { <PERSON>Header } from "./profile-header"
import { UserOrders } from "./user-orders"
import { AccountSettings } from "./account-settings"
import { Ta<PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { useLanguage } from "@/components/providers/language-provider"

interface User {
  id: string
  name: string
  email: string
  avatar: string
  joinDate: string
  totalOrders: number
  totalSpent: number
}

interface ProfilePageClientProps {
  user: User
}

export function ProfilePageClient({ user }: ProfilePageClientProps) {
  const { t } = useLanguage()

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <ProfileHeader user={user} />

        <Tabs defaultValue="orders" className="mt-8">
          <TabsList className="grid w-full grid-cols-2 lg:w-[400px]">
            <TabsTrigger value="orders">{t('orderHistory')}</TabsTrigger>
            <TabsTrigger value="settings">{t('accountSettings')}</TabsTrigger>
          </TabsList>

          <TabsContent value="orders" className="mt-6">
            <UserOrders userId={user.id} />
          </TabsContent>

          <TabsContent value="settings" className="mt-6">
            <AccountSettings user={user} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
