import type { Metadata } from "next"
import { ProfilePageClient } from "@/components/profile/profile-page-client"

export const metadata: Metadata = {
  title: "My Profile - PUBG Store",
  description: "Manage your account, view order history, and update your preferences.",
  robots: "noindex, nofollow", // Private page
}

// ## TODO: Fetch user data from Supabase: 'users' table and 'orders' table
const getUserData = async () => {
  // Placeholder data - replace with Supabase query
  return {
    user: {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      avatar: "/placeholder.svg?height=100&width=100",
      joinDate: "2023-06-15",
      totalOrders: 12,
      totalSpent: 1299,
    },
    orders: [
      {
        id: "ORD-001",
        date: "2024-01-15",
        status: "completed",
        total: 299,
        items: [{ name: "Premium Account - Level 100", price: 299, type: "account" }],
      },
      {
        id: "ORD-002",
        date: "2024-01-10",
        status: "completed",
        total: 99,
        items: [{ name: "Pro Vision Tool", price: 99, type: "hack" }],
      },
    ],
  }
}

export default async function ProfilePage() {
  const { user, orders } = await getUserData()

  return <ProfilePageClient user={user} />
}
