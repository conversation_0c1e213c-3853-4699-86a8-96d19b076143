"use client"

import { AccountsGrid } from "./accounts-grid"
import { AccountsFilters } from "./accounts-filters"
import { PageHeader } from "@/components/ui/page-header"
import { useLanguage } from "@/components/providers/language-provider"
import { Account } from "@/types/account"

interface AccountsPageContentProps {
  accounts: Account[]
}

export function AccountsPageContent({ accounts }: AccountsPageContentProps) {
  const { language } = useLanguage()

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <PageHeader
          title={language === "en" ? "PUBG Accounts" : "حسابات PUBG"}
          description={language === "en"
            ? "Premium gaming accounts with high levels, rare skins, and competitive ranks"
            : "حسابات ألعاب مميزة بمستويات عالية واسكنات نادرة ورتب تنافسية"}
        />

        {/* Compact Filters Above Content */}
        <div className="mb-6">
          <AccountsFilters />
        </div>

        {/* Main Content Grid */}
        <main>
          <AccountsGrid accounts={accounts} />
        </main>
      </div>
    </div>
  )
}
