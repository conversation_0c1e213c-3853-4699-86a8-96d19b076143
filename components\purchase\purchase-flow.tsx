"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { CreditCard, PaypalIcon, Bitcoin, Lock, CheckCircle } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"
import { Hack } from "@/types/hack"
import { Account } from "@/types/account"
import { PaymentMethod, generateOrderNumber } from "@/types/order"

interface PurchaseFlowProps {
  isOpen: boolean
  onClose: () => void
  product: Hack | Account | null
  productType: 'hack' | 'account'
}

export function PurchaseFlow({ isOpen, onClose, product, productType }: PurchaseFlowProps) {
  const { language, t } = useLanguage()
  const [step, setStep] = useState<'review' | 'payment' | 'processing' | 'success'>('review')
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('credit_card')
  const [isProcessing, setIsProcessing] = useState(false)

  if (!product) return null

  const handlePurchase = async () => {
    setIsProcessing(true)
    setStep('processing')

    // ## Database: Implement actual purchase logic
    // 1. Create order in Supabase
    // 2. Process payment with selected method
    // 3. Update order status
    // 4. Send confirmation email
    
    // Simulate processing
    setTimeout(() => {
      setStep('success')
      setIsProcessing(false)
    }, 3000)
  }

  const handleClose = () => {
    setStep('review')
    setPaymentMethod('credit_card')
    setIsProcessing(false)
    onClose()
  }

  const orderNumber = generateOrderNumber()

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {step === 'review' && t('reviewPurchase')}
            {step === 'payment' && t('paymentDetails')}
            {step === 'processing' && t('processingPayment')}
            {step === 'success' && t('purchaseComplete')}
          </DialogTitle>
        </DialogHeader>

        {step === 'review' && (
          <div className="space-y-6">
            {/* Product Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t('orderSummary')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center">
                    <span className="text-2xl">
                      {productType === 'hack' ? '🎮' : '👤'}
                    </span>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold">{product.name[language]}</h3>
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {product.description[language]}
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="outline" className="text-xs">
                        {t(productType)}
                      </Badge>
                      <Badge
                        variant={product.status === "active" ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {t(product.status)}
                      </Badge>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-orange-400">
                      ${product.price}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Order Details */}
            <Card>
              <CardContent className="p-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>{t('subtotal')}:</span>
                    <span>${product.price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{t('tax')}:</span>
                    <span>$0.00</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between text-lg font-semibold">
                    <span>{t('total')}:</span>
                    <span className="text-orange-400">${product.price}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex gap-3">
              <Button variant="outline" onClick={handleClose} className="flex-1">
                {t('cancel')}
              </Button>
              <Button
                onClick={() => setStep('payment')}
                className="flex-1 bg-orange-500 hover:bg-orange-600"
              >
                {t('continueToPayment')}
              </Button>
            </div>
          </div>
        )}

        {step === 'payment' && (
          <div className="space-y-6">
            {/* Payment Method Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t('paymentMethod')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                  <Button
                    variant={paymentMethod === 'credit_card' ? 'default' : 'outline'}
                    onClick={() => setPaymentMethod('credit_card')}
                    className="h-16 flex flex-col items-center gap-1"
                  >
                    <CreditCard className="w-5 h-5" />
                    <span className="text-xs">{t('creditCard')}</span>
                  </Button>
                  <Button
                    variant={paymentMethod === 'paypal' ? 'default' : 'outline'}
                    onClick={() => setPaymentMethod('paypal')}
                    className="h-16 flex flex-col items-center gap-1"
                  >
                    <div className="w-5 h-5 bg-blue-600 rounded flex items-center justify-center">
                      <span className="text-white text-xs font-bold">P</span>
                    </div>
                    <span className="text-xs">{t('paypal')}</span>
                  </Button>
                  <Button
                    variant={paymentMethod === 'crypto' ? 'default' : 'outline'}
                    onClick={() => setPaymentMethod('crypto')}
                    className="h-16 flex flex-col items-center gap-1"
                  >
                    <Bitcoin className="w-5 h-5" />
                    <span className="text-xs">{t('crypto')}</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Payment Form */}
            {paymentMethod === 'credit_card' && (
              <Card>
                <CardContent className="p-4 space-y-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <Label htmlFor="cardNumber">
                        {language === "en" ? "Card Number" : "رقم البطاقة"}
                      </Label>
                      <Input id="cardNumber" placeholder="1234 5678 9012 3456" />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="expiry">
                          {language === "en" ? "Expiry Date" : "تاريخ الانتهاء"}
                        </Label>
                        <Input id="expiry" placeholder="MM/YY" />
                      </div>
                      <div>
                        <Label htmlFor="cvv">CVV</Label>
                        <Input id="cvv" placeholder="123" />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="cardName">
                        {language === "en" ? "Cardholder Name" : "اسم حامل البطاقة"}
                      </Label>
                      <Input id="cardName" placeholder={language === "en" ? "John Doe" : "أحمد محمد"} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Security Notice */}
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Lock className="w-4 h-4" />
              <span>
                {t('paymentSecure')}
              </span>
            </div>

            <div className="flex gap-3">
              <Button variant="outline" onClick={() => setStep('review')} className="flex-1">
                {t('back')}
              </Button>
              <Button
                onClick={handlePurchase}
                className="flex-1 bg-orange-500 hover:bg-orange-600"
                disabled={isProcessing}
              >
                {language === "en" ? `Pay $${product.price}` : `ادفع $${product.price}`}
              </Button>
            </div>
          </div>
        )}

        {step === 'processing' && (
          <div className="text-center py-8">
            <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold mb-2">
              {language === "en" ? "Processing Payment..." : "معالجة الدفع..."}
            </h3>
            <p className="text-muted-foreground">
              {t('processingMessage')}
            </p>
          </div>
        )}

        {step === 'success' && (
          <div className="text-center py-8">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {t('purchaseSuccessful')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {language === "en"
                ? `Order #${orderNumber} ${t('orderConfirmed')}`
                : `${t('orderConfirmed')} الطلب #${orderNumber}`}
            </p>
            <div className="space-y-3">
              <Button onClick={handleClose} className="w-full">
                {t('continueShopping')}
              </Button>
              <Button variant="outline" className="w-full">
                {language === "en" ? "View Order Details" : "عرض تفاصيل الطلب"}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
