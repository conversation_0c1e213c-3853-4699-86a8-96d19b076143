"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, CreditCard, Eye, Calendar } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"
import { ProductViewModal } from "@/components/ui/product-view-modal"
import { PurchaseFlow } from "@/components/purchase/purchase-flow"

import { Account } from "@/types/account"

interface AccountsGridProps {
  accounts: Account[]
}

export function AccountsGrid({ accounts }: AccountsGridProps) {
  const { language } = useLanguage()
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isPurchaseOpen, setIsPurchaseOpen] = useState(false)
  const [purchaseAccount, setPurchaseAccount] = useState<Account | null>(null)

  const handleViewAccount = (account: Account) => {
    setSelectedAccount(account)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedAccount(null)
  }

  const handleBuyNow = (account: Account) => {
    setPurchaseAccount(account)
    setIsPurchaseOpen(true)
  }

  const handleClosePurchase = () => {
    setIsPurchaseOpen(false)
    setPurchaseAccount(null)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  }

  return (
    <>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-2 sm:gap-4 md:gap-6"
      >
        {accounts.map((account) => (
          <motion.div key={account.id} variants={itemVariants}>
            <Card
              className="bg-background border-border hover:border-orange-400 transition-all duration-300 group h-full flex flex-col cursor-pointer touch-target"
              onClick={() => handleViewAccount(account)}
            >
              <CardHeader className="p-0">
                <div className="relative overflow-hidden rounded-t-lg">
                  <Image
                    src={account.imageUrl || "/mido-logo.jpg"}
                    alt={account.name[language]}
                    width={300}
                    height={300}
                    className="w-full aspect-square object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  {account.isFeatured && (
                    <Badge className="absolute top-1 left-1 sm:top-2 sm:left-2 bg-orange-500 text-white text-xs px-1 py-0.5">
                      <Star className="w-2.5 h-2.5 sm:w-3 sm:h-3 mr-0.5 sm:mr-1" />
                      <span className="hidden sm:inline">{language === "en" ? "Featured" : "مميز"}</span>
                      <span className="sm:hidden">{language === "en" ? "★" : "★"}</span>
                    </Badge>
                  )}
                  <Badge
                    className="absolute top-1 right-1 sm:top-2 sm:right-2 text-xs px-1 py-0.5"
                    variant={
                      account.status === "active" ? "default" :
                      account.status === "sold" ? "destructive" : "secondary"
                    }
                  >
                    <span className="hidden sm:inline">{account.status}</span>
                    <span className="sm:hidden">
                      {account.status === "active" ? "●" : account.status === "sold" ? "✕" : "○"}
                    </span>
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="p-2 sm:p-3 md:p-4 flex-1 card-content-height">
                <div className="space-y-2 sm:space-y-3 h-full flex flex-col">
                  <div className="flex-1">
                    <h3 className="font-bold text-xs sm:text-sm md:text-base line-clamp-2 mb-1 leading-tight">
                      {account.name[language]}
                    </h3>
                    <p className="text-muted-foreground text-xs sm:text-sm line-clamp-3 leading-tight">
                      {account.description[language]}
                    </p>
                  </div>

                  <div className="flex items-center justify-between mt-auto">
                    <div className="flex items-center space-x-1 text-xs text-muted-foreground min-w-0 flex-1">
                      <Calendar className="w-2.5 h-2.5 sm:w-3 sm:h-3 flex-shrink-0" />
                      <span className="text-truncate-mobile">{new Date(account.createdAt).toLocaleDateString()}</span>
                    </div>
                    <div className="text-sm sm:text-lg md:text-xl font-bold text-orange-400 flex-shrink-0 ml-2">
                      ${account.price}
                    </div>
                  </div>
                </div>
              </CardContent>

              <CardFooter className="p-2 sm:p-3 md:p-4 pt-0">
                <div className="w-full">
                  <Button
                    size="sm"
                    className="w-full bg-orange-500 hover:bg-orange-600 text-white text-xs sm:text-sm touch-target min-h-[36px] sm:min-h-[40px]"
                    disabled={account.status !== "active"}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleBuyNow(account)
                    }}
                  >
                    <CreditCard className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                    <span className="truncate">{language === "en" ? "Buy Now" : "اشتري الآن"}</span>
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      <ProductViewModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        product={selectedAccount}
        productType="account"
      />

      <PurchaseFlow
        isOpen={isPurchaseOpen}
        onClose={handleClosePurchase}
        product={purchaseAccount}
        productType="account"
      />
    </>
  )
}
