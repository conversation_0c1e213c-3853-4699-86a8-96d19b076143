"use client"

import { usePathname } from "next/navigation"
import { useLanguage } from "@/components/providers/language-provider"

interface HreflangTagsProps {
  baseUrl?: string
}

export function HreflangTags({ baseUrl = "https://rngstore.vip" }: HreflangTagsProps) {
  const pathname = usePathname()
  const { language } = useLanguage()

  // Generate hreflang URLs for current page
  const generateHreflangUrls = () => {
    const cleanPath = pathname.replace(/^\/ar/, '') || '/'
    
    return {
      en: `${baseUrl}${cleanPath}`,
      ar: `${baseUrl}/ar${cleanPath}`,
      default: `${baseUrl}${cleanPath}`
    }
  }

  const urls = generateHreflangUrls()

  return (
    <>
      <link rel="alternate" hrefLang="en" href={urls.en} />
      <link rel="alternate" hrefLang="ar" href={urls.ar} />
      <link rel="alternate" hrefLang="x-default" href={urls.default} />
      
      {/* Canonical URL based on current language */}
      <link rel="canonical" href={language === 'ar' ? urls.ar : urls.en} />
      
      {/* Language-specific meta tags */}
      <meta name="language" content={language} />
      <meta name="content-language" content={language} />
      
      {/* RTL support for Arabic */}
      {language === 'ar' && (
        <>
          <meta name="direction" content="rtl" />
          <meta name="text-direction" content="rtl" />
        </>
      )}
    </>
  )
}

// SEO-optimized language switcher component
interface LanguageSwitcherSEOProps {
  className?: string
}

export function LanguageSwitcherSEO({ className = "" }: LanguageSwitcherSEOProps) {
  const { language, setLanguage } = useLanguage()
  const pathname = usePathname()

  const switchLanguage = (newLang: 'en' | 'ar') => {
    setLanguage(newLang)
    
    // Update URL for SEO
    const cleanPath = pathname.replace(/^\/ar/, '') || '/'
    const newPath = newLang === 'ar' ? `/ar${cleanPath}` : cleanPath
    
    // Use window.location for proper SEO navigation
    window.location.href = newPath
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <button
        onClick={() => switchLanguage('en')}
        className={`px-3 py-1 rounded text-sm transition-colors ${
          language === 'en' 
            ? 'bg-orange-500 text-white' 
            : 'text-gray-400 hover:text-white'
        }`}
        aria-label="Switch to English"
        hrefLang="en"
      >
        EN
      </button>
      <button
        onClick={() => switchLanguage('ar')}
        className={`px-3 py-1 rounded text-sm transition-colors ${
          language === 'ar' 
            ? 'bg-orange-500 text-white' 
            : 'text-gray-400 hover:text-white'
        }`}
        aria-label="التبديل إلى العربية"
        hrefLang="ar"
      >
        عربي
      </button>
    </div>
  )
}

// Multilingual structured data helper
export function generateMultilingualStructuredData(data: {
  name: { en: string; ar: string }
  description: { en: string; ar: string }
  url: string
  type: string
}) {
  return {
    "@context": "https://schema.org",
    "@type": data.type,
    name: data.name.en,
    alternateName: data.name.ar,
    description: data.description.en,
    url: data.url,
    inLanguage: ["en", "ar"],
    potentialAction: {
      "@type": "ReadAction",
      target: [
        {
          "@type": "EntryPoint",
          urlTemplate: data.url,
          inLanguage: "en"
        },
        {
          "@type": "EntryPoint", 
          urlTemplate: `${data.url}/ar`,
          inLanguage: "ar"
        }
      ]
    }
  }
}

// SEO-friendly language detection and redirect
export function useLanguageRedirect() {
  const { language } = useLanguage()
  const pathname = usePathname()

  // Check if user is on correct language path
  const isOnCorrectPath = () => {
    if (language === 'ar') {
      return pathname.startsWith('/ar')
    } else {
      return !pathname.startsWith('/ar')
    }
  }

  // Generate correct path for current language
  const getCorrectPath = () => {
    const cleanPath = pathname.replace(/^\/ar/, '') || '/'
    return language === 'ar' ? `/ar${cleanPath}` : cleanPath
  }

  return {
    isOnCorrectPath: isOnCorrectPath(),
    correctPath: getCorrectPath()
  }
}

// Multilingual meta tags generator
export function generateMultilingualMeta(content: {
  title: { en: string; ar: string }
  description: { en: string; ar: string }
  keywords: { en: string; ar: string }
}, language: 'en' | 'ar') {
  return {
    title: content.title[language],
    description: content.description[language],
    keywords: content.keywords[language],
    openGraph: {
      title: content.title[language],
      description: content.description[language],
      locale: language === 'ar' ? 'ar_SA' : 'en_US',
    },
    twitter: {
      title: content.title[language],
      description: content.description[language],
    }
  }
}

// RTL-aware CSS class helper
export function getRTLClasses(language: 'en' | 'ar') {
  return {
    textAlign: language === 'ar' ? 'text-right' : 'text-left',
    direction: language === 'ar' ? 'rtl' : 'ltr',
    fontFamily: language === 'ar' ? 'font-cairo' : 'font-inter',
    spacing: language === 'ar' ? 'space-x-reverse' : '',
  }
}

// SEO-optimized breadcrumbs for multilingual sites
interface BreadcrumbItem {
  name: { en: string; ar: string }
  url: string
}

export function generateBreadcrumbStructuredData(
  items: BreadcrumbItem[],
  language: 'en' | 'ar'
) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name[language],
      item: language === 'ar' ? `/ar${item.url}` : item.url
    }))
  }
}
