"use client"

import { motion } from "framer-motion"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { useLanguage } from "@/components/providers/language-provider"

export function CTASection() {
  const { language } = useLanguage()

  return (
    <section className="py-20 bg-gradient-to-r from-orange-600 to-orange-500">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        className="container mx-auto px-4 text-center"
      >
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
          {language === "en" ? "Ready to Elevate Your Game?" : "هل أنت مستعد لرفع مستواك في اللعب؟"}
        </h2>
        <p className="text-white/90 mb-10 max-w-2xl mx-auto">
          {language === "en"
            ? "Browse our premium accounts and tools now and join thousands of satisfied gamers."
            : "تصفح حساباتنا المميزة وأدواتنا الآن وانضم إلى آلاف اللاعبين السعداء."}
        </p>
        <Link href="/accounts">
          <Button size="lg" className="bg-black hover:bg-zinc-900 text-orange-400">
            {language === "en" ? "Get Started" : "ابدأ الآن"}
          </Button>
        </Link>
      </motion.div>
    </section>
  )
}
