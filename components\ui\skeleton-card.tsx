"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"

interface SkeletonCardProps {
  className?: string
  isSquare?: boolean
}

export function SkeletonCard({ className = "", isSquare = true }: SkeletonCardProps) {
  return (
    <Card className={`bg-background border-border h-full flex flex-col ${className}`}>
      <CardHeader className="p-0">
        <div className="relative overflow-hidden rounded-t-lg">
          <div 
            className={`w-full bg-zinc-800 animate-pulse ${
              isSquare ? "aspect-square" : "h-32 sm:h-40"
            }`}
          />
          {/* Badge skeleton */}
          <div className="absolute top-2 left-2 sm:top-3 sm:left-3 w-12 h-5 bg-zinc-700 rounded animate-pulse" />
        </div>
      </CardHeader>

      <CardContent className="p-2 sm:p-3 md:p-4 flex-1 card-content-height">
        <div className="space-y-2 sm:space-y-3 h-full flex flex-col">
          {/* Title skeleton */}
          <div className="space-y-1">
            <div className="h-4 bg-zinc-800 rounded animate-pulse" />
            <div className="h-4 bg-zinc-800 rounded w-3/4 animate-pulse" />
          </div>
          
          {/* Description skeleton */}
          <div className="space-y-1">
            <div className="h-3 bg-zinc-800 rounded animate-pulse" />
            <div className="h-3 bg-zinc-800 rounded w-5/6 animate-pulse" />
          </div>

          {/* Features skeleton */}
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <div className="w-1.5 h-1.5 bg-zinc-700 rounded-full" />
              <div className="h-3 bg-zinc-800 rounded flex-1 animate-pulse" />
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-1.5 h-1.5 bg-zinc-700 rounded-full" />
              <div className="h-3 bg-zinc-800 rounded w-4/5 animate-pulse" />
            </div>
          </div>

          {/* Price skeleton */}
          <div className="flex justify-end">
            <div className="h-6 w-16 bg-zinc-800 rounded animate-pulse" />
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-2 sm:p-3 md:p-4 pt-0">
        <div className="w-full">
          <div className="h-8 sm:h-10 bg-zinc-800 rounded animate-pulse touch-target" />
        </div>
      </CardFooter>
    </Card>
  )
}

interface SkeletonGridProps {
  count?: number
  className?: string
  isSquare?: boolean
}

export function SkeletonGrid({ count = 6, className = "", isSquare = true }: SkeletonGridProps) {
  return (
    <div className={`grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-2 sm:gap-4 md:gap-6 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <SkeletonCard key={index} isSquare={isSquare} />
      ))}
    </div>
  )
}

// Skeleton for featured products section
export function FeaturedProductsSkeleton() {
  return (
    <section className="py-16 bg-background">
      <div className="container mx-auto px-4">
        {/* Section header skeleton */}
        <div className="text-center mb-12">
          <div className="h-8 bg-zinc-800 rounded w-64 mx-auto mb-4 animate-pulse" />
          <div className="h-4 bg-zinc-800 rounded w-96 mx-auto animate-pulse" />
        </div>

        {/* Accounts section skeleton */}
        <div className="mb-16">
          <div className="h-6 bg-zinc-800 rounded w-48 mb-6 animate-pulse" />
          <SkeletonGrid count={3} />
        </div>

        {/* Hacks section skeleton */}
        <div>
          <div className="h-6 bg-zinc-800 rounded w-48 mb-6 animate-pulse" />
          <SkeletonGrid count={3} />
        </div>
      </div>
    </section>
  )
}

// Skeleton for page headers
export function PageHeaderSkeleton() {
  return (
    <div className="text-center mb-8">
      <div className="h-8 bg-zinc-800 rounded w-64 mx-auto mb-4 animate-pulse" />
      <div className="h-4 bg-zinc-800 rounded w-96 mx-auto animate-pulse" />
    </div>
  )
}
