"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useLanguage } from "@/components/providers/language-provider"

export function AuthTabs() {
  const { language } = useLanguage()
  const [loginData, setLoginData] = useState({ email: "", password: "" })
  const [signupData, setSignupData] = useState({ name: "", email: "", password: "", confirmPassword: "" })

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault()
    // ## TODO: Implement login with Supabase Auth
    console.log("Login:", loginData)
  }

  const handleSignup = (e: React.FormEvent) => {
    e.preventDefault()
    // ## TODO: Implement signup with Supabase Auth
    console.log("Signup:", signupData)
  }

  return (
          <Card className="bg-black border-zinc-700">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl text-white">
          {language === "en" ? "Welcome to PUBG Store" : "مرحباً بك في متجر PUBG"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="login" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="login">{language === "en" ? "Login" : "تسجيل الدخول"}</TabsTrigger>
            <TabsTrigger value="signup">{language === "en" ? "Sign Up" : "إنشاء حساب"}</TabsTrigger>
          </TabsList>

          <TabsContent value="login" className="space-y-4">
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="login-email" className="text-zinc-300">
                  {language === "en" ? "Email" : "البريد الإلكتروني"}
                </Label>
                <Input
                  id="login-email"
                  type="email"
                  value={loginData.email}
                  onChange={(e) => setLoginData({ ...loginData, email: e.target.value })}
                  className="bg-zinc-900 border-zinc-600 text-white"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="login-password" className="text-zinc-300">
                  {language === "en" ? "Password" : "كلمة المرور"}
                </Label>
                <Input
                  id="login-password"
                  type="password"
                  value={loginData.password}
                  onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                  className="bg-zinc-900 border-zinc-600 text-white"
                  required
                />
              </div>

              <Button type="submit" className="w-full bg-orange-500 hover:bg-orange-600 text-white">
                {language === "en" ? "Login" : "تسجيل الدخول"}
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="signup" className="space-y-4">
            <form onSubmit={handleSignup} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="signup-name" className="text-zinc-300">
                  {language === "en" ? "Full Name" : "الاسم الكامل"}
                </Label>
                <Input
                  id="signup-name"
                  value={signupData.name}
                  onChange={(e) => setSignupData({ ...signupData, name: e.target.value })}
                  className="bg-zinc-900 border-zinc-600 text-white"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="signup-email" className="text-zinc-300">
                  {language === "en" ? "Email" : "البريد الإلكتروني"}
                </Label>
                <Input
                  id="signup-email"
                  type="email"
                  value={signupData.email}
                  onChange={(e) => setSignupData({ ...signupData, email: e.target.value })}
                  className="bg-zinc-900 border-zinc-600 text-white"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="signup-password" className="text-zinc-300">
                  {language === "en" ? "Password" : "كلمة المرور"}
                </Label>
                <Input
                  id="signup-password"
                  type="password"
                  value={signupData.password}
                  onChange={(e) => setSignupData({ ...signupData, password: e.target.value })}
                  className="bg-zinc-900 border-zinc-600 text-white"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="signup-confirm" className="text-zinc-300">
                  {language === "en" ? "Confirm Password" : "تأكيد كلمة المرور"}
                </Label>
                <Input
                  id="signup-confirm"
                  type="password"
                  value={signupData.confirmPassword}
                  onChange={(e) => setSignupData({ ...signupData, confirmPassword: e.target.value })}
                  className="bg-zinc-900 border-zinc-600 text-white"
                  required
                />
              </div>

              <Button type="submit" className="w-full bg-orange-500 hover:bg-orange-600 text-white">
                {language === "en" ? "Create Account" : "إنشاء حساب"}
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
